import cls from 'classnames';
import './style.less';

interface IProps{
	loading: boolean;
	className?: string;
}
export default function Loading(props: IProps) {
	const { loading, className } = props;
	if (!loading) {
		return null;
	}
	return (
		<section className={cls('loading-wrap', className)}>
			<div className="loading">
				<div />
				<div />
				<div />
				<div />
				<div />
			</div>
		</section>
	);
}
