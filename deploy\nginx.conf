worker_processes  1;  ## Default: 1
error_log  /dianyi/log/content-site/error.log;
pid        /dianyi/app/nginx.pid;
worker_rlimit_nofile 8192;
events {
  worker_connections  4096;  ## Default: 1024
}

http {
  include       mime.types;
  default_type  application/octet-stream;
  log_format   main '$remote_addr - $remote_user [$time_local]  $status '
    '"$request" $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';
  sendfile     on;
  tcp_nopush   on;
  server_names_hash_bucket_size 128; # this seems to be required for some vhosts

  gzip_static on;
  gzip on;
	gzip_min_length 1k;
	gzip_buffers 4 16k;
	#gzip_http_version 1.0;
	gzip_comp_level 2;
	gzip_types text/plain application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
	gzip_vary off;
	gzip_disable "MSIE [1-6]\.";
  gzip_proxied any;

  client_max_body_size 100m;
  #关闭nginx中的头信息(server: nginx/1.16.1)
  server_tokens off;

  server {
    listen       80;
    access_log   /dianyi/log/content-site/access.log  main;

    location / {
      if ($request_filename ~* .*\.(?:htm|html|txt)$)
      {
		 add_header Cache-Control "no-store";
      	 root /dianyi/app/content_site/content-site;
      }
      if ($request_filename ~* .*\.(?:js|css|webp|jpg|png|gif|ico|eot|svg|ttf|woff|woff2|zip)$)
	  {
		 root /dianyi/app/content_site;
	  }
	  index index.html;
	  try_files $uri $uri/ /index.html;
	}
  }
}

