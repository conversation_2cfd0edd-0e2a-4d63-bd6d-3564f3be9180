import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import { LazyImg, LinkOrA } from '@components';
import { getFormattedDate, getFormattedNumberDate, getScaleImgSrc, timeAgo } from '@utils';
import { IContent } from '@types';
import TopNews from '../LastNews';

interface IProps {
  item: Array<IContent>
  lang: string
  showTime: boolean
  imgDomain: string
  category: string
  isShow: boolean
}
export default function ClassifiedNews(props: IProps) {
  const { item, lang, showTime, imgDomain, category,isShow } = props;
  return (
    <div className="classified-news">
      {
        category && isShow && <div className="title">{category}</div>
      }


      <div className="top">
        <LinkOrA path={`/${lang}/${item[0].path || item[0].id}`} className="banner" style={{ backgroundImage: `url(${getScaleImgSrc(item[0], imgDomain, '720*460')})` }}>
          <div className="category-banner" >
            <div className="category">{category}</div>
            {
              showTime && (
                <div className="content-two">{getFormattedDate(item[0].published_time)}</div>
              )
            }

          </div>
          <div className="banner-title">{item[0].title}</div>
          {
            showTime && (
              <div className="banner-desc">{timeAgo(item[0].published_time)}</div>
            )
          }
        </LinkOrA>
        <div className="top-item">
          {
            item.slice(1, item.length > 4 ? 4 : item.length).map(item => {
              return (
                <TopNews lang={lang} item={item} imgDomain={imgDomain} showTime={showTime} />
              )
            })
          }
        </div>
      </div>

    </div>

  )
}
