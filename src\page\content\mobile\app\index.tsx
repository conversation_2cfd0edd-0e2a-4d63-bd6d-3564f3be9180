import { useState, useEffect } from 'preact/hooks';
import { ContentL<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>wi<PERSON>, Foot<PERSON>, HeaderMobile } from '@components'
import { fetchList } from '@stores';
import { trackBiEvent, gtmEvent, getBrowser, adsComponentsInit, getAppBrowser, appendCreativeIdToHref, getTimeSincePost, getFormattedDate, getFormattedNumberDate } from '@utils';
import './style.less';
import { IContent, IContentList, IEnvMap } from '@types';
import { SEARCH_AD_OPTIONS, PATTERN, REG_SEARCH_TAG, REG_CONTENT_PAGINATION } from '@constants';

const adsMap: any = {};
const rsblock1 = {
	"container": "related-searches",
	"relatedSearches": 4 // 此相关搜索单元中应展示的相关搜索数量。如果未指定，则默认为 0
};
const rsblock2 = {
	"container": "related-searches-2",
	"relatedSearches": 4 // 此相关搜索单元中应展示的相关搜索数量。如果未指定，则默认为 0
};
const recommendAds = [
	{
		zone_key: '300x100_mobile_content_bottom_1',
		position: 1,
		width: 300,
		height: 100
	}
]

// 2024-4-2 特殊处理 区分dreame搜索广告收益
const DREAME_SITE = ['24787583', '24787636', 'site_24787027', '24787635', '24787003', 'site_24787028', '24787584'];
let imgElements: any = [];

interface IProps {
	seoDetailsData: IContent;
	seoEnvMap: IEnvMap;
	seoCategoryListData: Array<any>;
	seoCategoryDataList: IContentList
}
export default function App(props: IProps) {

	const { seoDetailsData, seoEnvMap, seoCategoryListData, seoCategoryDataList } = props;
	let { id, title, content, published_time, category_name, imgs, time_to_now } = seoDetailsData;
	const { zoneMap, isMobile, imgDomain, siteId, creativeId, theme } = seoEnvMap;
	const { logo } = theme || {};
	const [listData, setListData] = useState([]);
	const [showMore, setShowMore] = useState(false);
	const [contentHtml, setContentHtml] = useState(content);
	const [time, setTime] = useState(null);
	const [loading, setLoading] = useState(false);
	const [imgArr, setImgArr] = useState([]);
	const [appBrowser, setAppBrowser] = useState(null);
	const showTime = seoEnvMap && seoEnvMap.timeEnable && seoEnvMap.timeEnable.enable === false ? false : true;



	const isSpecial = siteId === '24788157';

	const renderAdsDOM = (id: string, sizeW: number, sizeH: number) => {
		const adsId = `ads-dom-${id}`;
		if (!adsMap[adsId]) {
			adsMap[adsId] = {
				width: sizeW,
				height: sizeH,
				zoneId: zoneMap[id],
			};
		}
		return (
			<div data-ads-id={adsId}></div>
		);
	};

	// 发送事件
	const sendEvent = (event_name: string, pageCategory?: string) => {
		const params: any = {
			event_name,
			page_id: pageCategory || category_name
		}
		if (event_name === 'news_page_leave') {
			params.page_stayduration = (new Date().getTime() - time) / 1000
		}
		trackBiEvent(event_name, params);
	}

	useEffect(() => {
		// 发送gtm事件: 内容页进入事件
		gtmEvent('content_pv', {
			event_label: title
		});
		stringHtmlInsetAdDom(content);
		if (imgs && imgs.length) {
			const w = document.documentElement.clientWidth;
			const useWebp = getBrowser() !== 'safari';
			const suffix = useWebp ? '.webp' : '.jpeg';
			const _imgs = imgs.map(item => `${imgDomain}/image/${w * 1.5}*450/${item}${suffix}?type=news_icon`);
			setImgArr(_imgs);
		}
		adsComponentsInit().then(() => {
			window.adsTag.cmd.push(() => {
				Object.keys(adsMap).forEach(adsId => {
					const { width, height, zoneId } = adsMap[adsId];
					const dom = document.querySelector(`[data-ads-id="${adsId}"]`);
					window.adsTag.renderAds(dom, width, height, zoneId);
				});
			});
		});
		fetchList({
			page_size: 10,
			page_index: 1,
			last_published_time: published_time,
			last_id: id,
			category: category_name,
			site_id: siteId
		}).then((res: IContentList) => {
			const { data } = res;
			setListData(data);
		});
		setLoading(true);
		setTime(new Date().getTime());
		setAppBrowser(getAppBrowser())
	}, []);

	useEffect(() => {
		if (!loading) {
			return
		}
		sendEvent('news_page');
		let isSendLeave = false;

		window.addEventListener('pagehide', function () {
			if (!isSendLeave) {
				sendEvent('news_page_leave');
				isSendLeave = true;
			}
		}, false);
		window.addEventListener('pagehide', function () {
			if (!isSendLeave) {
				sendEvent('news_page_leave');
				isSendLeave = true;
			}
		}, false);
	}, [loading])

	const replacer = (reg: string, p1: string) => {
		return `<iframe width="100%" height="315px" src="https://www.youtube.com/embed/${p1}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe/>`;
	}


	const createAdDom = (id: string) => {
		const dom = document.createElement('div');
		dom.setAttribute('id', id);
		dom.setAttribute('class', 'ad-wrap');
		return dom
	}

	// 给返回的内容里面插入渲染ad的dom
	const stringHtmlInsetAdDom = (stringHtml: string) => {
		const parser = new DOMParser();
		const doc = parser.parseFromString(stringHtml, 'text/html');
		let pElements: any = [];
		if (isSpecial) {

			imgElements = doc.querySelectorAll('img');
			imgElements.forEach((img: HTMLElement, index: number) => {
				img.setAttribute('loading', 'lazy');
				const adDom = createAdDom(`content-ad-${index}`);
				img.insertAdjacentElement('afterend', adDom);
			})



		} else {
			// console.log(2);
			pElements = doc.querySelectorAll('p');
			if (imgs && imgs.length) {
				const w = document.documentElement.clientWidth;
				const useWebp = getBrowser() !== 'safari';
				const suffix = useWebp ? '.webp' : '.jpeg';
				

				imgs.forEach((url, index) => {
					const targetP = pElements[index * 2]; // 每 2 个 p 插一次图
					if (targetP) {
						const img = doc.createElement('img');
						img.src = `${imgDomain}/image/${w * 1.5}*450/${url}${suffix}?type=news_icon`;
						img.alt = `image-${index}`;
						img.loading = 'lazy';
						targetP.insertAdjacentElement('afterend', img);
					}
				});
			}


			// console.log(imgs);

			// 包装 h3 标签下的文本内容
			const h3Elements = doc.querySelectorAll('h3');
			h3Elements.forEach(h3 => {
				let nextNode = h3.nextSibling;
				while (nextNode && (nextNode.nodeType !== Node.TEXT_NODE || !nextNode.textContent.trim())) {
					nextNode = nextNode.nextSibling;
				}
				if (nextNode && nextNode.nodeType === Node.TEXT_NODE) {
					const p = doc.createElement('p');
					p.textContent = nextNode.textContent;
					nextNode.parentNode.replaceChild(p, nextNode);
				}
			});
			// pElements = doc.querySelectorAll('p');
			// const secondParagraph = pElements[0];
			// if (secondParagraph) {
			// 	const relatedAdDom = createAdDom('related-searches');
			// 	secondParagraph.appendChild(relatedAdDom);
			// }
			// const bannerAdDom1 = pElements[0];
			// if (bannerAdDom1) {
			// 	const bannerDom = createAdDom('content-ad-1');
			// 	bannerAdDom1.appendChild(bannerDom);
			// }
			// const bannerAdDom2 = pElements[3];
			// if (bannerAdDom2) {
			// 	const bannerDom = createAdDom('content-ad-2');
			// 	bannerAdDom2.appendChild(bannerDom);
			// }
			// // if (bannerAdDom2) {
			// // 	const relatedAdDom = createAdDom('related-searches-2');
			// // 	bannerAdDom2.appendChild(relatedAdDom);
			// // }
			// const bannerAdDom3 = pElements[6];
			// if (bannerAdDom3) {
			// 	const bannerDom = createAdDom('content-ad-3');
			// 	bannerAdDom3.appendChild(bannerDom);
			// }
		}

		// 内容中清除 Relatedsearches 标识
		let relatedSearchesElements = Array.from(pElements).filter(p => p.textContent.trim() === "Relatedsearches");
		relatedSearchesElements.forEach(p => {
			p.parentNode.removeChild(p);
		})

		let innerHtml = doc.documentElement.querySelector('body').innerHTML;
		innerHtml = innerHtml.replace(PATTERN, replacer);
		innerHtml = innerHtml.replace(REG_SEARCH_TAG, '');
		innerHtml = innerHtml.replace(REG_CONTENT_PAGINATION, '');
		setContentHtml(innerHtml);
	}

	const handleMoreClick = () => {
		window.adsTag.cmd.push(() => {
			if (isSpecial) {
				imgElements.forEach((_: HTMLElement, index: number) => {
					if (index === 0) {
						window.adsTag.renderAds(document.querySelector(`#content-ad-${index}`), 300, 250, zoneMap['300x250_mobile_content_middle_1']);
					}
				})
			} else {
				if (document.querySelector('#content-ad-1')) {
					window.adsTag.renderAds(document.querySelector('#content-ad-1'), 300, 250, zoneMap['300x250_mobile_content_middle_1']);
				}
				if (document.querySelector('#content-ad-2')) {
					window.adsTag.renderAds(document.querySelector('#content-ad-2'), 300, 250, zoneMap['300x250_mobile_content_middle_2']);
				}
				if (document.querySelector('#content-ad-3')) {
					window.adsTag.renderAds(document.querySelector('#content-ad-3'), 300, 250, zoneMap['300x250_mobile_content_middle_3']);
				}
			}
		});
		// 相关搜索广告配置
		// const pageOptions = {
		// 	...SEARCH_AD_OPTIONS
		// }
		// let resultsPageBaseUrl = '//' + location.host + '/title/search-query';
		// if (DREAME_SITE.includes(siteId)) {
		// 	resultsPageBaseUrl = '//site-record.asia/title/search-query'
		// }
		// pageOptions.query = title;
		// pageOptions.terms = title.replace(/ /gi, ',');
		// pageOptions.resultsPageBaseUrl = resultsPageBaseUrl;
		// window._googCsa('ads', pageOptions, rsblock1, rsblock2);
		setShowMore(false);
	}

	const renderImg = () => {
		if (imgArr.length) {
			if (imgArr.length === 1) {
				return (
					<div className="carousel">
						<img src={imgArr[0]} alt="" />
					</div>
				)
			}
			if (imgArr.length > 1) {
				return (
					<>
						<div className="carousel">
							<Swiper swiperName="swiper_1" swiperList={imgArr} swiperHeight={250} swipeWidth={document.documentElement.clientWidth - 32} swiperGap={0} />
						</div>
					</>
				)
			}
		} else {
			return null
		}
	}

	if (!loading) {
		return null
	}

	return (
		<div className="content-page">
			<HeaderMobile seoEnvMap={seoEnvMap} seoCategoryListData={seoCategoryListData} activeCategory={category_name} isShowSearch={false} />
			<section className="content-wrap">
				<div className="content-text">
					<div className="text-wrap" style={{ maxHeight: showMore ? '96px' : 'none' }}>
						{/* {renderImg()} */}
						<h1 className="title">
							{title}
							{
								showTime ?
									(
										<span className="title-time">
											{
												getFormattedNumberDate(published_time)
											}
										</span>
									) : null
							}

						</h1>
						{/* <BarHeader headerName="Instructions" /> */}
						<div className="text" dangerouslySetInnerHTML={{ __html: contentHtml }} />
					</div>
				</div>
			</section>
		</div>
	);
}
