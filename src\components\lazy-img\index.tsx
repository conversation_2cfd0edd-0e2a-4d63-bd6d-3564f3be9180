import { useEffect, useRef, useState } from 'preact/hooks';
import ErrorImg from '@assets/images/news.png';
interface LazyImageProps {
	src: string;
	alt: string;
	className?: string;
}

export default function LazyImage(props: LazyImageProps) {
	const { src, alt, className } = props;
	const [isVisible, setIsVisible] = useState(false);
	const imgRef = useRef<HTMLImageElement>(null);
	useEffect(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						setIsVisible(true);
						observer.disconnect();
					}
				});
			},
			{ threshold: 0.1 }
		)
		if (imgRef.current) {
			observer.observe(imgRef.current);
		}
		return () => {
			if (imgRef.current) {
				observer.unobserve(imgRef.current);
			}
		}
	}, []);
	return (
		<img
			className={className}
			ref={imgRef}
			src={isVisible ? src : ErrorImg}
			alt={alt}
		/>
	)
}
