import { useState, useEffect } from 'preact/hooks';
import { ContentCard, ContentList, HeaderMobile, PageHeader, Footer, ContentLoadMore, LinkOrA } from '@components'
import { adsComponentsInit, trackBiEvent, appendCreativeIdToHref, getScaleImgSrc, getFormattedDate, timeAgo } from '@utils';
import { fetchList } from '@stores';
import './style.less';
import { IContent, IContentList, IEnvMap } from '@types';
import { getLocale } from '@utils/locales';
import ListScroll from './ListScroll';
import LazyImage from '@components/lazy-img';
import nosearch from '@assets/images/nosearch.png';

interface IProps {
	seoCategory: string;
	seoCategoryListData: Array<any>;
	seoCategoryListItemData: Array<IContent>;
	seoEnvMap: IEnvMap
	categoryId: string
}
// 每次拉取条数
const PAGE_SIZE = 12;
let nowPage = 2;

const recommendAds = [
	{
		zone_key: '300x100_mobile_index_content_bottom_2',
		width: 300,
		height: 100
	}
]

const adsMap: Record<string, { width: number; height: number; zoneId: string }> = {};
export default function App(props: IProps) {
	const { seoCategory, seoEnvMap, seoCategoryListData, seoCategoryListItemData, categoryId } = props;
	const { imgDomain, zoneMap, siteId, creativeId, mainDomain, lang } = seoEnvMap;

	const [adsInitialized, setAdsInitialized] = useState(false);
	const [listData, setListData] = useState(seoCategoryListItemData);
	const [hasMore, setHasMore] = useState(true);

	const [time, setTime] = useState(null);

	const showTime = seoEnvMap && seoEnvMap.timeEnable && seoEnvMap.timeEnable.enable === false ? false : true;


	useEffect(() => {
		adsComponentsInit().then(() => {
			setAdsInitialized(true);
		});

		setTime(new Date().getTime());
	}, []);

	const loadMoreData = async () => {
		if (!hasMore) return;
		fetchList({
			page_index: nowPage,
			page_size: PAGE_SIZE,
			category: categoryId,
			// 后端返回的推荐newList是根据published_time排序的，所以这里需要根据published_time来获取下一页数据
			last_published_time: listData[listData.length - 1].published_time,
			last_id: listData[listData.length - 1].id
		}).then((res) => {
			// 将新数据追加到原来的推荐列表中
			setListData((prevData) => [...prevData, ...res.data]);
			nowPage++;
			if (res.data.length < PAGE_SIZE) {
				setHasMore(false);
			}
		})
	}



	useEffect(() => {
		if (adsInitialized) {
			Object.keys(adsMap).forEach(adsId => {
				const { width, height, zoneId } = adsMap[adsId];
				const dom = document.querySelector(`[data-ads-id="${adsId}"]`);
				if (dom && !dom.innerHTML) {
					window.adsTag.renderAds(dom, width, height, zoneId);
				}
			});
		}
	}, [listData, adsInitialized])


	// 发送事件
	const sendEvent = (event_name: string) => {
		const params: any = {
			event_name,
			page_id: seoCategory
		}
		if (event_name === 'page_leave') {
			params.page_stayduration = (new Date().getTime() - time) / 1000
		}
		trackBiEvent(event_name, params);
	}

	useEffect(() => {
		sendEvent('page_reach');
		let isSendLeave = false;

		window.addEventListener('pagehide', function () {
			if (!isSendLeave) {
				sendEvent('page_leave');
				isSendLeave = true;
			}
		}, false);
		window.addEventListener('pagehide', function () {
			if (!isSendLeave) {
				sendEvent('page_leave');
				isSendLeave = true;
			}
		}, false);
	}, [])
	return (
		<section className="list-mobile-wrap">
			<HeaderMobile seoEnvMap={seoEnvMap} seoCategoryListData={seoCategoryListData} activeCategory={seoCategory} isShowSearch={false} />
			{
				(seoCategoryListItemData && seoCategoryListItemData.length > 0) && (
					<div className="list-mobile-content">
						<LinkOrA path={`/${lang}/${seoCategoryListItemData[0].path || seoCategoryListItemData[0].id}`} className="banner" style={{ backgroundImage: `url(${getScaleImgSrc(seoCategoryListItemData[0], imgDomain, '720*460')})` }}>
							<div className="bg-banner">
								<div className="category-banner" >
									<div className="category">{seoCategory}</div>
									{
										showTime && (
											<div className="content-two">{getFormattedDate(seoCategoryListItemData[0].published_time)}</div>
										)
									}

								</div>
								<div className="banner-title">{seoCategoryListItemData[0].title}</div>
								{
									showTime && (
										<div className="banner-desc">{timeAgo(seoCategoryListItemData[0].published_time)}</div>
									)
								}
							</div>
						</LinkOrA>
						<div className="titles">{getLocale(seoEnvMap.lang, "latest news")}</div>
						<div className="listScroll">
							<div className="listScroll-item">
								<ListScroll item={listData.slice(1, listData.length)} lang={lang} imgDomain={imgDomain} category={seoCategory} showTime={showTime} />
							</div>
							<ContentLoadMore seoEnvMap={seoEnvMap} loadMore={loadMoreData} hasMore={hasMore} />
						</div>
					</div>
				 )// : (
				// 	<div className="no-data-content">
				// 		<div className="no-data">
				// 			<LazyImage src={nosearch} alt="no-data" />
				// 			<span>{getLocale(lang,"no search result yet")}</span>
				// 		</div>
				// 	</div>
				// )
			}

		</section>
	);
}
