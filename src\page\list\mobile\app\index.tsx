import { useState, useEffect } from 'preact/hooks';
import { ContentCard, ContentList, HeaderMobile, PageHeader ,Footer, ContentLoadMore} from '@components'
import { adsComponentsInit, trackBiEvent, appendCreativeIdToHref } from '@utils';
import { fetchList } from '@stores';
import './style.less';
import { IContent, IContentList, IEnvMap } from '@types';

interface IProps {
	seoCategory: string;
	seoCategoryListData: Array<string>;
	seoCategoryListItemData: Array<IContent>;
	seoEnvMap: IEnvMap
}
// 每次拉取条数
const PAGE_SIZE = 12;
let nowPage = 2;

const recommendAds = [
	{
		zone_key: '300x100_mobile_index_content_bottom_2',
		width: 300,
		height: 100
	}
]

const adsMap: Record<string, { width: number; height: number; zoneId: string }> = {};
export default function App(props: IProps) {
	const { seoCategory, seoEnvMap, seoCategoryListData, seoCategoryListItemData } = props;
	const { imgDomain, zoneMap, siteId, creativeId, mainDomain } = seoEnvMap;

	const [adsInitialized, setAdsInitialized] = useState(false);
	const [listData, setListData] = useState(seoCategoryListItemData);
	const [ hasMore, setHasMore ] = useState(true);

	const [time, setTime] = useState(null);


	useEffect(() => {
		adsComponentsInit().then(() => {
			setAdsInitialized(true);
		});

		setTime(new Date().getTime());
	}, []);

	const loadMoreData = async () => {
		if (!hasMore) return;
		fetchList({
			page_index: nowPage,
			page_size: PAGE_SIZE,
			category: seoCategoryListData[0],
			// 后端返回的推荐newList是根据published_time排序的，所以这里需要根据published_time来获取下一页数据
			last_published_time: listData[listData.length -1].published_time,
			last_id: listData[listData.length -1].id
		}).then((res) => {
			// 将新数据追加到原来的推荐列表中
			setListData((prevData) => [...prevData, ...res.data]);
			nowPage++;
			if (res.data.length < PAGE_SIZE) {
				setHasMore(false);
			}
		})
	}

	

	useEffect(() => {
		if (adsInitialized) {
			Object.keys(adsMap).forEach(adsId => {
				const { width, height, zoneId } = adsMap[adsId];
				const dom = document.querySelector(`[data-ads-id="${adsId}"]`);
				if (dom && !dom.innerHTML) {
					window.adsTag.renderAds(dom, width, height, zoneId);
				}
			});
		}
	}, [listData, adsInitialized])


	// 发送事件
	const sendEvent = (event_name: string) => {
		const params:any = {
			event_name,
			page_id: seoCategory
		}
		if (event_name === 'page_leave') {
			params.page_stayduration = (new Date().getTime() - time) / 1000
		}
		trackBiEvent(event_name, params);
	}

	useEffect(() => {
		sendEvent('page_reach');
		let isSendLeave = false;

		window.addEventListener('pagehide', function(){
			if (!isSendLeave) {
				sendEvent('page_leave');
				isSendLeave = true;
			}
		}, false);
		window.addEventListener('pagehide', function(){
			if (!isSendLeave) {
				sendEvent('page_leave');
				isSendLeave = true;
			}
		}, false);
	}, [])
	return (
		<section className="list-mobile-wrap">
			list - mobile
		</section>
	);
}
