const express = require('express');
const { version, name, components } = require('../package.json');
const wgtNodeUtils = require('wgt-node-utils');
const proxy = require('http-proxy-middleware');
const API_DOMAIN = require('../api-domain.json');
const axios = require('axios');
const path = require('path');
const { API_PREFIX, DEFAULT_SITE_ID, API_GAME_BRIDGE_PREFIX, DEFAULT_LANGUAGE_LIST } = require('./constants');

const app = express();
// 禁用express生成的标识
app.disable('x-powered-by');
axios.interceptors.response.use(
	resp => {
		return resp;
	},
	error => {
		if (error.response && error.response.status === 500) {
			return error
		}
	}
)

// theme 默认值
const themeDefault = {
	logo: '/content-site/assets/logo.svg',
	icon: '/content-site/site-icon.ico',
	title: 'content'
}
// 每个页面的最终对应的model名称
const pagePaths = {
	homeMobile: 'index-mobile',
	homeDefault: 'index-pc',
	listMobile: 'list-mobile',
	listPc: 'list-pc',
	contentMobile: 'content-mobile',
	contentPc: 'content-pc',
	privacyPolicy: 'privacy-policy',
	searchResultMobile: 'search-result-mobile',
	searchResultPc: 'search-result-pc',
	contactMe: 'contact-me',
	termsOfUs: 'terms-of-use',
	aboutUs: 'about-us',
	error: 'error'
}
// 动态加载模块
const loadPage = (pageName) => {
	const path = `./page/${version}/${pagePaths[pageName]}.ssr.js`;
	return require(path).page.default;
}
// 首页
const homeMobileSSR = loadPage('homeMobile');
const homeDefaultSSR = loadPage('homeDefault');

// 列表页
const listMobileSSR = loadPage('listMobile');
const listPcSSR = loadPage('listPc');
// 内容页
const contentMobileSSR = loadPage('contentMobile');
const contentPcSSR = loadPage('contentPc');
// 隐私政策
const privacyPolicySSR = loadPage('privacyPolicy');
// 联系我们
const contactMeSSR = loadPage('contactMe');

// 错误页面
const errorSSR = loadPage('error');

// 搜索结果页面
const searchResultPcSSR = loadPage('searchResultPc');
const searchResultMobileSSR = loadPage('searchResultMobile');

// // terms of us 页面
const termsOfUsSSR = loadPage('termsOfUs')
// // about us 页面
const aboutUsSSR = loadPage('aboutUs')

const port = 8084;

// 非本地环境中使用时需要在node中增加变量，本地访问线上接口时通过webpack-dev.config.js
const ENV = process.env.ENV;
const { host, apiDomain, adsTagHref, imgDomain } = API_DOMAIN[ENV];

const API_PATH = host + API_PREFIX;
const API_GAME_BRIDGE_PATH = host + API_GAME_BRIDGE_PREFIX;
console.log('host====', host);
console.log('API_Path', API_PATH);

const siteIdPath = `${host}/gamebridge/v1/site/domain`;
const siteConfigPath = `${host}/gamebridge/v1/site/config`;
const contactPath = `${host}/gamebridge/v1/contact`;

// 获取site相关配置
const getSiteConfigByHostname = async function (url, hostname, imgDomain, creativeId) {
	return new Promise((resolve) => {
		const params = {
			domain: hostname,
			creativeId
		}
		axios.get(url, { params }).then(res => {
			let siteData = res.data.data;
			// 如果未获取到数据，使用myworldfix.com做为默认数据
			if (!siteData) {
				siteData = {
					siteId: '24786993',
					zoneMap: {
						'160x600_pc_category_left': '45697',
						'160x600_pc_category_right': '45698',
						'160x600_pc_content_left': '45695',
						'160x600_pc_content_right': '45696',
						'160x600_pc_index_left': '45690',
						'160x600_pc_index_right': '45691',
						'300x100_mobile_content_bottom_1': '44524',
						'300x100_mobile_content_bottom_2': '44525',
						'300x100_mobile_index_content_bottom_1': '44611',
						'300x100_mobile_index_content_bottom_2': '44612',
						'300x250_mobile_content_middle_1': '44521',
						'300x250_mobile_content_middle_2': '44522',
						'300x250_mobile_content_middle_3': '44523',
						'300x250_mobile_content_top': '43918',
						'300x250_mobile_content_top_technology': '44159',
						'300x250_mobile_index_top': '43919',
						'300x250_mobile_index_top_technology': '44158',
						'300x250_mobile_template1_top': '47189',
						'300x250_pc_index_left': '45693',
						'300x250_pc_index_right': '45694',
						'728x90_pc_index_list': '45692',
						'interstitial_mobile': '43920',
						'interstitial_mobile_technology': '44160',
						'interstitial_pc': '45716',
						'interstitial_pc1': '43921'
					},
					theme: {
						icon: '',
						title: '',
						logo: '',
						templateType: ''
					}
				}
			}
			const { icon, logo, title, disableHeader, ...otherTheme } = siteData.theme || {};
			siteData.theme = {
				icon: icon ? `${imgDomain}/site_icon/${icon}` : '/content-site/site-icon.ico',
				logo: logo ? `${imgDomain}/site_logo/${logo}` : '/content-site/assets/logo.svg',
				title: title || hostname,
				...otherTheme
			}
			resolve(siteData);
		});
	});
}

// 获取政策页面相关内容
const getPolicyAndContact = async function (siteId, lang, type) {
	return new Promise((resolve) => {
		const params = {
			siteId,
			lang,
			type
		}
		axios.get(`${API_GAME_BRIDGE_PATH}/site/${siteId}/custom_privacy_v2?language=${lang.toUpperCase()}&property_type=${type}`).then(res => {
			let policyData = res.data.data.properties || {};
			resolve(policyData);
		}).catch(error => {
			console.error('Error fetching policy data:', error);
			resolve({});
		});
	});
}

// 本地环境, 开启代理: [development: 本地静态资源+mock接口, qa: 本地静态资源+qa接口, pro: 本地静态资源+pro接口]
if (ENV === 'development' || ENV === 'qa' || ENV === 'pro') {
	// 开发环境代理(测试与产品环境不会使用到): mock数据 8084 => 2204
	app.use(proxy(API_PREFIX, { target: host }));

	// 开发环境代理(测试与产品环境不会使用到): 静态资源 8084 => 2204
	app.use(proxy(`/${name}/`, { target: 'http://127.0.0.1:2204' }));
}

// 获取其它的静态资源: 访问路径为/xxxx/xx.xx，线上环境通过Gears配置后，这项不会用到
app.use(express.static(path.join(__dirname, '..')));

// 获取其它的静态资源: 访问路径为/xx.xx，比如/ads.txt会通过这里
app.use(express.static(path.join(__dirname, `../${name}/`)));

// 健康检查使用的接口
app.get(['^/health$'], function (req, res) {
	res.end('{name: "ContentSiteV21"}');
});


let languageList = [];
// 通过该app.all()进行处理后，未被过滤掉的next函数里，lan必将存在一个正常值
app.all('*', async function (req, res, next) {
	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	const siteLanguageCodeList = await wgtNodeUtils.getSiteLanguage(`${host}/feed/site/${siteId}/category/language`, 'content');
	let { pathname } = req._parsedUrl;
	let lan;

	if (pathname) {
		lan = pathname.split('/')[1];
	}
	// 取LANGUAGE_CODE_LIST和siteLanguageCodeList的交集
	languageList = DEFAULT_LANGUAGE_LIST.filter(lang => siteLanguageCodeList.includes(lang.code.toLowerCase()));
	// 验证语言是否可用，如不可用时使用默认语言路径（语言列表需要使用接口替换，替换时需注意调用频率
	if (languageList.some(item => item.code === lan)) {
		next();
		return;
	} else {
		lan = wgtNodeUtils.getCloudFrontViewerCountry(req, languageList);
		req.url = `/${lan}${req.url}`;
	}

	// 过滤掉非法路径
	if (wgtNodeUtils.excludeIllegalPath(pathname)) {
		res.writeHead(403, { 'Content-Type': 'text/plain' });
		res.end('Request Denied'); // 提示请求被拒绝
		return;
	}
	next();
});



// 首页
app.get(['^/:lang/', '^/:lang/index.html', '^/:lang/home.html'], async function (req, res) {
	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const { lang } = req.params;
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		adsTagHref,
		isMobile,
		theme: themeDefault,
		system,
		imgDomain,
		creativeId,
		widgetId,
		languageList,
		lang
	}
	// 是否展示时间
	const showTime = axios.get(`${API_GAME_BRIDGE_PATH}/site/${siteId}/property/content_time_show`);


	// 先获取siteConfig
	const siteConfigRes = await getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId);
	let SSR;
	// let notDefault;
	if (isMobile) {
		SSR = homeMobileSSR;
	} else {
		const { theme } = siteConfigRes;
		const { templateType } = theme || {};
		if (!templateType || templateType === 'default') {
			SSR = homeDefaultSSR;
		}
	}
	const f2eFiles = wgtNodeUtils.getModelF2eFiles('index', isMobile, false);
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');

	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);
	const categoryList = await axios.get(`${API_PATH}/site/${siteId}/category`, {
		params: {
			page_size: isMobile ? 5 : 10,
			page_index: 1,
			language: lang.toUpperCase()
		}
	});
	let seoCategoryListData = categoryList.data.data || [];

	let axiosArr = [];
	// let seoFiveNewsList = [];
	// 默认模板 请求接口
	// if (!notDefault) {
	const fetchNewsList = axios.get(`${API_PATH}/summary/list`, {
		params: {
			page_size: isMobile ? 4 : 31,
			page_index: 1,
			category: seoCategoryListData[0].id ? seoCategoryListData[0].id : 'News',
			site_id: siteId
		}
	})
	axiosArr.push(fetchNewsList);

	const fetchNewsLists = axios.get(`${API_PATH}/summary/list`, {
		params: {
			page_size: isMobile ? 8 : 7,
			page_index: 1,
			// category: seoCategoryListData[0] ?  seoCategoryListData[0] : 'News',
			site_id: siteId
		}
	})
	// }

	// 获取分类的详细数据
	const featchCategoryList =
		seoCategoryListData.map((category, index) => {
			return axios
				.get(`${API_PATH}/summary/list`, {
					params: {
						page_size: isMobile ? index === (seoCategoryListData.length - 1) ? 7 : 4 : 12,
						page_index: 1,
						category: category.id,
						site_id: siteId
					},
				})
				.then((res) => {
					return {
						category_name: category.name,
						listData: res.data.data,
					};
				});
		}) || [];
	let seoCategoryDataList = await Promise.all(featchCategoryList);
	let sdkConfig = null;
	if (creativeId) {
		sdkConfig = axios.get(`${API_PATH}/site/${siteId}/site-config?creative_id=${creativeId}`);
	} else {
		sdkConfig = axios.get(`${API_PATH}/site/${siteId}/site-config`);
	}
	axios.all([wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: true }), sdkConfig, showTime, fetchNewsLists, ...axiosArr])
		.then(axios.spread((sdkF2eFilesRes, sdkSiteConfigRes, showTimeRes, seofetchNewsLists, ...axiosArrRes) => {
			let sdkSiteConfigObj = {};
			if (sdkSiteConfigRes && sdkSiteConfigRes.data && sdkSiteConfigRes.data.site_id === siteId) {
				sdkSiteConfigObj = sdkSiteConfigRes.data;
			}

			// 合并SDK 文件
			Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });

			Object.assign(seoEnvMap, siteConfigRes, { sdkSiteConfig: sdkSiteConfigObj }, { timeEnable: showTimeRes.data.data.properties || {} });
			let seoNewsList = [];
			// let seoIndexSwiper = [];
			// if (!notDefault) {
			// seoIndexSwiper = axiosArrRes[0].data.data.slice(0,5)
			seoNewsList = axiosArrRes[0].data.data.slice(0, 31) || [];
			// }

			res.end(SSR(ENV, {
				seoEnvMap,
				seoCategoryListData,
				seoActiveCategory: seoCategoryListData[0],
				// seoFiveNewsList,
				seoNewsList,
				seoCategoryDataList,
				seoFetchNewsLists: seofetchNewsLists.data.data,
				...(isMobile ? { isMobile } : {})
			}, f2eFiles));
		}))
		.catch(err => {
			wgtNodeUtils.appendLog(`首页`, ENV, components, err, { useSendFeiShu: false });
			res.end(errorSSR(ENV, {
				seoEnvMap
			}, errorF2eFiles));
		});
});

// 列表页
app.get(['^/:lang/list/:category'], async function (req, res) {
	const { category } = req.params;
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const { lang } = req.params;
	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const SSR = isMobile ? listMobileSSR : listPcSSR;

	const f2eFiles = wgtNodeUtils.getModelF2eFiles('list', isMobile, false);
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		adsTagHref,
		isMobile,
		system,
		theme: themeDefault,
		imgDomain,
		creativeId,
		widgetId,
		languageList,
		lang
	}
	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);

	const categoryList = axios.get(`${API_PATH}/site/${siteId}/category/list`, {
		params: {
			page_size: 10,
			page_index: 1,
			language: lang.toUpperCase()
		}
	});

	// 是否展示时间
	const showTime = axios.get(`${API_GAME_BRIDGE_PATH}/site/${siteId}/property/content_time_show`);

	let sdkConfig = null;
	if (creativeId) {
		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config?creative_id=${creativeId}`);
	} else {
		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config`);
	}

	const categoryDataList = axios.get(`${API_PATH}/summary/list`, {
		params: {
			page_size: 12,
			page_index: 1,
			category: category,
			site_id: siteId
		}
	})
	// 获取分类的详细数据
	const categoryLists = await axios.get(`${API_PATH}/site/${siteId}/category`, {
		params: {
			page_size: 10,
			page_index: 1,
			language: lang.toUpperCase()
		}
	});
	
	
	let seoCategoryListData = categoryLists.data.data || [];
	const featchCategoryList =
		seoCategoryListData.map((category) => {
			return axios
				.get(`${API_PATH}/summary/list`, {
					params: {
						page_size: 12,
						page_index: 1,
						category: category.id,
						site_id: siteId
					},
				})
				.then((res) => {
					return {
						category_name: category,
						listData: res.data.data,
					};
				});
		}) || [];
	let seoCategoryDataList = await Promise.all(featchCategoryList);
	axios.all([wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: true }), sdkConfig, getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId), categoryList, categoryDataList, showTime])
		.then(axios.spread((sdkF2eFilesRes, sdkSiteConfigRes, siteConfigRes, categoryListRes, categoryDataListRes, showTimeRes) => {
			let sdkSiteConfigObj = {};
			if (sdkSiteConfigRes && sdkSiteConfigRes.data && sdkSiteConfigRes.data.site_id === siteId) {
				sdkSiteConfigObj = sdkSiteConfigRes.data;
			}

			// 合并SDK 文件
			Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });

			Object.assign(seoEnvMap, siteConfigRes, { sdkSiteConfig: sdkSiteConfigObj }, { timeEnable: showTimeRes.data.data.properties || {} });
			let seoCategoryListData = [];
			seoCategoryListData = categoryListRes.data.data;
			let seoCategoryListItemData = []
			seoCategoryListItemData = categoryDataListRes.data.data || []

			res.end(SSR(ENV, {
				seoEnvMap,
				seoCategory: category,
				seoCategoryListData,
				seoCategoryListItemData,
				seoCategoryDataList,
			}, f2eFiles));
		})).catch(err => {
			wgtNodeUtils.appendLog(`列表页面`, ENV, components, err, { useSendFeiShu: false });
			res.end(errorSSR(ENV, {
				seoEnvMap
			}, errorF2eFiles));
		})
});

//主页隐私页面
app.get(['^/:lang/privacy-policy'], async function (req, res) {
	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	// 获取主域名
	const mainDomain = wgtNodeUtils.getMainDomainByHostname(req.hostname);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const { lang } = req.params;
	const siteConfigRes = await getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId);

	const f2eFiles = wgtNodeUtils.getModelF2eFiles('privacy-policy');
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		system,
		adsTagHref,
		mainDomain,
		creativeId,
		widgetId,
		languageList,
		lang
	}
	Object.assign(seoEnvMap, siteConfigRes);
	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);
	axios.all([
		wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: true }),
		getPolicyAndContact(siteId, lang, 'custom_privacy')])
		.then(axios.spread((sdkF2eFilesRes, policyAndContactRes) => {
			// 合并SDK 文件
			Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });
			// 增加头信息Content-Type, 在开启gzip时会用到
			const seoPolicyAndContact = policyAndContactRes || {};
			res.setHeader('Content-Type', 'text/html; charset=utf-8');
			res.end(privacyPolicySSR(ENV, {
				seoEnvMap,
				seoPolicyAndContact
			}, f2eFiles));
		})).catch(err => {
			wgtNodeUtils.appendLog(`政策页面`, ENV, components, err, { useSendFeiShu: false });
			res.end(errorSSR(ENV, {
				seoEnvMap
			}, errorF2eFiles));
		})
});

//contact Me 页面
app.get(['^/:lang/contact-me'], async function (req, res) {
	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { lang } = req.params;
	const f2eFiles = wgtNodeUtils.getModelF2eFiles('contact-me');
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		system,
		adsTagHref,
		creativeId,
		widgetId,
		isMobile,
		imgDomain,
		lang,
		languageList,
		mainDomain: wgtNodeUtils.getMainDomainByHostname(req.hostname),

		// languageList
	}

	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);
	const siteConfigRes = await getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId);
	Object.assign(seoEnvMap, siteConfigRes);
	axios.all([
		wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: true }),
		getPolicyAndContact(siteId, lang, 'contact_us')
	]).then(axios.spread((sdkF2eFilesRes, policyAndContactRes) => {
		// 合并SDK 文件
		Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });
		res.setHeader('Content-Type', 'text/html');
		const seoPolicyAndContact = policyAndContactRes || {};
		res.end(contactMeSSR(ENV, {
			seoEnvMap,
			seoPolicyAndContact
		}, f2eFiles));
	})).catch(err => {
		wgtNodeUtils.appendLog(`联系我们`, ENV, components, err, { useSendFeiShu: false });
		res.end(errorSSR(ENV, {
			seoEnvMap
		}, errorF2eFiles));
	})

});

app.get(["^/:lang/terms-of-use"], async function (req, res) {
	const siteId = await wgtNodeUtils.getSiteByHostname(
		siteIdPath,
		req.hostname,
		DEFAULT_SITE_ID
	);
	// 获取主域名
	const mainDomain = wgtNodeUtils.getMainDomainByHostname(req.hostname);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const siteConfigRes = await getSiteConfigByHostname(
		siteConfigPath,
		req.hostname,
		imgDomain,
		creativeId
	);
	const { lang } = req.params;
	const f2eFiles = wgtNodeUtils.getModelF2eFiles("terms-of-use");
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles("error");
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		adsTagHref,
		mainDomain,
		system,
		creativeId,
		widgetId,
		isMobile,
		imgDomain,
		languageList,
		lang,
	};
	const categoryList = await axios.get(
		`${API_PATH}/site/${siteId}/category/list`,
		{
			params: {
				page_size: 20,
				page_index: 1,
				language: lang.toUpperCase()
			},
		}
	);
	let seoCategoryListData = categoryList.data.data || [];
	// 获取隐私政策和联系我们的内容
	const policyAndContactRes = await getPolicyAndContact(siteId, lang, 'terms_of_us');
	Object.assign(seoEnvMap, siteConfigRes);
	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);

	const sdkF2eFilesRes = await wgtNodeUtils.getSdkF2eFiles(
		API_PATH,
		adsTagHref,
		ENV,
		{ components, useSendFeiShu: false }
	);

	// 合并SDK 文件
	Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });
	res.end(
		termsOfUsSSR(
			ENV,
			{
				seoEnvMap,
				mainDomain,
				seoCategoryListData,
				policyAndContactRes
			},
			f2eFiles
		)
	);
});

// 关于我们页面
app.get(["^/:lang/about-us"], async function (req, res) {
	const siteId = await wgtNodeUtils.getSiteByHostname(
		siteIdPath,
		req.hostname,
		DEFAULT_SITE_ID
	);
	// 获取主域名
	const mainDomain = wgtNodeUtils.getMainDomainByHostname(req.hostname);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const siteConfigRes = await getSiteConfigByHostname(
		siteConfigPath,
		req.hostname,
		imgDomain,
		creativeId
	);
	const { lang } = req.params;
	const f2eFiles = wgtNodeUtils.getModelF2eFiles("about-us");
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles("error");
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		adsTagHref,
		mainDomain,
		system,
		creativeId,
		widgetId,
		isMobile,
		imgDomain,
		languageList,
		lang,
	};
	const categoryList = await axios.get(
		`${API_PATH}/site/${siteId}/category/list`,
		{
			params: {
				page_size: 20,
				page_index: 1,
				language: lang.toUpperCase()
			},
		}
	);
	let seoCategoryListData = categoryList.data.data || [];
	Object.assign(seoEnvMap, siteConfigRes);

	const sdkF2eFilesRes = await wgtNodeUtils.getSdkF2eFiles(
		API_PATH,
		adsTagHref,
		ENV,
		{ components, useSendFeiShu: false }
	);

	// 获取隐私政策和联系我们的内容
	const policyAndContactRes = await getPolicyAndContact(siteId, lang, 'about_us');

	// 合并SDK 文件
	Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });
	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);
	res.end(
		aboutUsSSR(
			ENV,
			{
				seoEnvMap,
				mainDomain,
				seoCategoryListData,
				policyAndContactRes
			},
			f2eFiles
		)
	);
});

// 搜索结果页面
// app.get(['^/:lang/search-result'], async function(req, res) {
// 	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
// 	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
// 	const system = wgtNodeUtils.getCloudFrontSystem(device);
// 	const isMobile = system.isPhone || system.isAndroid;
// 	const { w_cid, w_id, search_result } = req.query;
// 	const creativeId = w_cid;
// 	const widgetId = w_id;
// 	const { lang } = req.params;
// 	// 向f2e传递的环境变量
// 	const seoEnvMap = {
// 		apiDomain,
// 		hostname: req.hostname,
// 		siteId,
// 		adsTagHref,
// 		system,
// 		isMobile,
// 		theme: themeDefault,
// 		imgDomain,
// 		creativeId,
// 		widgetId,
// 		languageList,
// 		lang
// 	}

// 	// 先获取siteConfig
// 	const siteConfigRes = await getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId);
// 	const SSR = isMobile ? searchResultMobileSSR : searchResultPcSSR;
// 	const f2eFiles = wgtNodeUtils.getModelF2eFiles('search-result', isMobile, false);
// 	// error页面的前端资源
// 	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');

// 	// 添加公用头信息
// 	wgtNodeUtils.addCommonHeadersInfo(res);

// 	const searchResultList  = axios.get(`${API_PATH} `, {
// 		params: {
// 			page_size: 31,
// 			page_index: 1,
// 			keyword: search_result,
// 			site_id: siteId
// 		}
// 	})

// 	let sdkConfig = null;
// 	if (creativeId) {
// 		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config?creative_id=${creativeId}`);
// 	} else {
// 		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config`);
// 	}
// 	axios.all([wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: true } ),sdkConfig, searchResultList])
// 		.then(axios.spread((sdkF2eFilesRes, sdkSiteConfigRes, searchResultListRes) => {
// 			let sdkSiteConfigObj = {};
// 			if (sdkSiteConfigRes && sdkSiteConfigRes.data && sdkSiteConfigRes.data.site_id === siteId) {
// 				sdkSiteConfigObj = sdkSiteConfigRes.data;
// 			}

// 			// 合并SDK 文件
// 			Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });

// 			Object.assign(seoEnvMap, siteConfigRes, { sdkSiteConfig: sdkSiteConfigObj });
// 			let seoSearchResultList = [];
// 			seoSearchResultList = searchResultListRes.data.data || [];

// 			res.end(SSR(ENV, {
// 				seoEnvMap,
// 				seoSearchResultList,
// 				seoSearchResult: search_result
// 			}, f2eFiles));
// 		}))
// 		.catch(err => {
// 			wgtNodeUtils.appendLog(`搜索结果页面`, ENV, components, err,{  useSendFeiShu: false, req, usePageUrl: true });
// 			res.end(errorSSR(ENV, {
// 				seoEnvMap
// 			}, errorF2eFiles));
// 		});
// });

// 搜索结果页面
app.get(['^/:lang/search-result'], async function (req, res) {
	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { w_cid, w_id, search_result } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const { lang } = req.params;

	// 是否展示时间
	const showTime = await axios.get(`${API_GAME_BRIDGE_PATH}/site/${siteId}/property/content_time_show`);
	const timeEnable = showTime.data.properties || {};
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId,
		adsTagHref,
		system,
		isMobile,
		theme: themeDefault,
		imgDomain,
		creativeId,
		widgetId,
		languageList,
		lang,
		timeEnable
	}

	// 先获取siteConfig
	const siteConfigRes = await getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId);
	const SSR = isMobile ? searchResultMobileSSR : searchResultPcSSR;
	const f2eFiles = wgtNodeUtils.getModelF2eFiles('search-result', isMobile, false);
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');

	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);

	const searchResultList = axios.get(`${API_PATH}/summary/listForSearch`, {
		params: {
			page_size: 31,
			page_index: 1,
			keyword: search_result,
			site_id: siteId
		}
	})

	const categoryList = await axios.get(`${API_PATH}/site/${siteId}/category`, {
		params: {
			page_size: 10,
			page_index: 1,
			language: lang.toUpperCase()
		}
	});
	let seoCategoryListData = categoryList.data.data || [];


	const fetchCategoriesList = seoCategoryListData.map(
		category => {
			return axios.get(`${API_PATH}/summary/list`, {
				params: {
					page_size: 18,
					page_index: 1,
					category: category.id,
					site_id: siteId
				}
			}).then(res => ({
				// category_name: category,
				// path: category.path,
				listData: res.data.data
			}))
		})
	let seoCategoryDataList = await Promise.all(fetchCategoriesList);

	let sdkConfig = null;
	if (creativeId) {
		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config?creative_id=${creativeId}`);
	} else {
		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config`);
	}
	axios.all([wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: false }), sdkConfig, searchResultList, showTime])
		.then(axios.spread((sdkF2eFilesRes, sdkSiteConfigRes, searchResultListRes, showTimeRes) => {
			let sdkSiteConfigObj = {};
			if (sdkSiteConfigRes && sdkSiteConfigRes.data && sdkSiteConfigRes.data.site_id === siteId) {
				sdkSiteConfigObj = sdkSiteConfigRes.data;
			}

			// 合并SDK 文件
			Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });

			Object.assign(seoEnvMap, siteConfigRes, { sdkSiteConfig: sdkSiteConfigObj }, { timeEnable: showTimeRes.data.data.properties || {} });
			let seoSearchResultList = [];
			seoSearchResultList = searchResultListRes.data.data || [];

			res.end(SSR(ENV, {
				seoEnvMap,
				seoSearchResultList,
				seoSearchResult: search_result,
				seoCategoryListData,
				seoCategoryListItemData: seoCategoryDataList[0].listData
			}, f2eFiles));
		}))
		.catch(err => {
			console.log(err, '搜索');

			wgtNodeUtils.appendLog(`搜索结果页面`, ENV, components, err, { useSendFeiShu: false, req, usePageUrl: true });
			res.end(errorSSR(ENV, {
				seoEnvMap
			}, errorF2eFiles));
		});
});

// 详情页
app.get(['^/:lang/:contentId'], async function (req, res, next) {
	const { contentId } = req.params;
	const { w_cid, w_id } = req.query;
	const creativeId = w_cid;
	const widgetId = w_id;
	const device = wgtNodeUtils.getCloudFrontDevice(req.headers);
	const system = wgtNodeUtils.getCloudFrontSystem(device);
	const isMobile = system.isPhone || system.isAndroid;
	const { lang } = req.params;

	const SSR = isMobile ? contentMobileSSR : contentPcSSR;

	const f2eFiles = wgtNodeUtils.getModelF2eFiles('content', isMobile, false);
	// error页面的前端资源
	const errorF2eFiles = wgtNodeUtils.getModelF2eFiles('error');
	// 防止未能匹配的资源进入详情页处理逻辑
	// if (!/^\w+$/.test(contentId)) {
	// 	next();
	// 	return;
	// }

	const siteId = await wgtNodeUtils.getSiteByHostname(siteIdPath, req.hostname, DEFAULT_SITE_ID);
	// 向f2e传递的环境变量
	const seoEnvMap = {
		apiDomain,
		hostname: req.hostname,
		siteId: siteId,
		adsTagHref,
		isMobile,
		theme: themeDefault,
		imgDomain,
		system,
		creativeId,
		widgetId,
		languageList,
		lang
	}
	// 添加公用头信息
	wgtNodeUtils.addCommonHeadersInfo(res);
	const details = axios.get(`${API_PATH}/${encodeURIComponent(contentId)}`, {
		params: {
			site_id: siteId
		}
	});

	const categoryList = axios.get(`${API_PATH}/site/${siteId}/category/list`, {
		params: {
			page_size: 10,
			page_index: 1,
			language: lang.toUpperCase()
		}
	});

	// 是否展示时间
	const showTime = axios.get(`${API_GAME_BRIDGE_PATH}/site/${siteId}/property/content_time_show`);

	let sdkConfig = null;
	if (creativeId) {
		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config?creative_id=${creativeId}`);
	} else {
		sdkConfig = axios.get(`${host}/gamebridge/v1/site/${siteId}/site-config`);
	}
	const fetchList = [sdkConfig, await getSiteConfigByHostname(siteConfigPath, req.hostname, imgDomain, creativeId), details, categoryList];

	if (!isMobile) {
		const categoryList = axios.get(`${API_PATH}/site/${siteId}/category/list`, {
			params: {
				page_size: 10,
				page_index: 1
			}
		});
		fetchList.push(categoryList)
	}

	axios.all([wgtNodeUtils.getSdkF2eFiles(API_GAME_BRIDGE_PATH, adsTagHref, ENV, { components, useSendFeiShu: true }), showTime, ...fetchList])
		.then(axios.spread(async (sdkF2eFilesRes, showTimeRes, sdkSiteConfigRes, siteConfigRes, detailsRes, categoryListRes) => {
			let sdkSiteConfigObj = {};
			if (sdkSiteConfigRes && sdkSiteConfigRes.data && sdkSiteConfigRes.data.site_id === siteId) {
				sdkSiteConfigObj = sdkSiteConfigRes.data;
			}

			// 合并SDK 文件
			Object.assign(f2eFiles, { adsTagStr: sdkF2eFilesRes });

			Object.assign(seoEnvMap, siteConfigRes, { sdkSiteConfig: sdkSiteConfigObj }, { timeEnable: showTimeRes.data.data.properties || {} });
			let seoDetailsData = {};
			if (detailsRes.data) {
				seoDetailsData = detailsRes.data.data;
				seoDetailsData.content = seoDetailsData.content.replace(/^<pre><code\s*>(.*?)<\/code><\/pre>/, '');
			}
			let seoCategoryListData = [];
			if (categoryListRes) {
				seoCategoryListData = categoryListRes.data.data;
			}
			// 获取分类的详细数据
			const featchCategoryList =
				axios.get(`${API_PATH}/summary/list`, {
					params: {
						page_size: 20,
						page_index: 1,
						category: seoDetailsData.category[0],
						site_id: siteId,
					},
				})
					.then((res) => {
						return {
							category_name: seoDetailsData.category_name,
							listData: res.data.data,
						};
					});

			let seoCategoryDataList = await featchCategoryList;
			res.end(SSR(ENV, {
				seoEnvMap,
				seoDetailsData,
				seoCategoryListData,
				seoCategoryDataList
			}, f2eFiles));
		}))
		.catch(err => {
			wgtNodeUtils.appendLog(`咨询详情页[${contentId}]`, ENV, components, err, { useSendFeiShu: false, req, usePageUrl: true });
			res.end(errorSSR(ENV, {
				seoEnvMap
			}, errorF2eFiles));
		});
});


// 启动前先读取静态资源
wgtNodeUtils.readF2eFiles(name, version, __dirname, false).then(function () {
	app.listen(port, function () {
		console.log('Listening on port %d', port);
		console.log('NODE host:', host);
		if (ENV === 'development') {
			console.log('WEB host:', 'http://127.0.0.1:8084');
		}
	});
}).catch(() => {
	wgtNodeUtils.appendLog('node启动失败: 静态资源读取错误', ENV, components);
});
