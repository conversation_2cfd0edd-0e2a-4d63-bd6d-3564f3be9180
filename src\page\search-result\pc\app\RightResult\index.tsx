import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import LazyImage from '@components/lazy-img';
import { appendCreativeIdToHref, getScaleImgSrc } from '@utils';
import { trackBiEvent, gtmEvent } from '@utils';
import { getFormattedDate } from '@utils';
import cls from 'classnames';
interface IProps {
    seoNewsList: Array<any>;
    title: string;
    seoEnvMap: any;
    seoCategoryListData: Array<any>;
}
export default function RightResult(props: IProps) {
    const { seoNewsList, title, seoEnvMap, seoCategoryListData } = props;
    const { imgDomain, lang, creativeId, timeEnable } = seoEnvMap;
    const showTime = timeEnable && timeEnable.enable === false ? false : true;
    const [categoryname, setCategoryname] = useState('')
    const onContentClick = (type: number, title: string, category: number) => {
        if (type === 2) return;
        const params = {
            event_name: 'news_click',
            page_id: category
        }
        trackBiEvent('news_click', params);

        // 发送gtm事件: 内容点击事件
        gtmEvent('item_click', {
            event_label: title
        });
    }
    useEffect(() => {
        seoCategoryListData.map((item) => {
            if (item.id === seoNewsList[0].category[0]) {
                setCategoryname(item.name)
            }
        })
    })
    return (
        <div className="right-content-card">
            <svg xmlns="http://www.w3.org/2000/svg" width="292" height="2" viewBox="0 0 292 2" fill="none">
                <path d="M1 1H291" stroke="#DDDDDD" stroke-width="2" stroke-linecap="round" />
            </svg>
            <div className="right-content-card-title">{title}</div>
            <div className="right-card-content-warp">
                {
                    seoNewsList.map((item, index) => {
                        return (
                            <a
                                className={cls('right-card-content-item')}
                                onClick={() => { onContentClick(item.type, item.title, item.category[0]) }}
                                href={appendCreativeIdToHref(`/${lang}/${item.path || item.id}`, creativeId)}>
                                <div className="item-number">
                                    #{index + 1}
                                </div>
                                <div className={cls('item-content')}>

                                    <div className={cls('item-title')}>

                                        {item.title}
                                    </div>
                                    <div className={cls('item-time')}>
                                        {categoryname} {showTime && `| ` + getFormattedDate(item.published_time)}
                                    </div>
                                </div>
                            </a>
                        )
                    })
                }
            </div>
            <div className="right-svg">
                <svg xmlns="http://www.w3.org/2000/svg" width="292" height="2" viewBox="0 0 292 2" fill="none">
                    <path d="M1 1H291"  stroke-width="2" stroke-linecap="round" />
                </svg>
            </div>
        </div>
    )
}
