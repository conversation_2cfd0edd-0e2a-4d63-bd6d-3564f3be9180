import './style.less';
import { IContent, IContentList, IEnvMap } from '@types';
import { HeaderMobile } from '@components';
import ClassifiedNews from './ClassifiedNews';
import Scroll from './Scroll';



interface IProps {
	isMobile: boolean
	seoActiveCategory: string
	seoCategoryListData: Array<any>
	seoEnvMap: IEnvMap
	seoFetchNewsLists: Array<IContent>
	seoCategoryDataList: Array<IContentList>
}
export default function App(props: IProps) {
	const { isMobile, seoActiveCategory, seoCategoryListData, seoEnvMap, seoFetchNewsLists, seoCategoryDataList } = props;
	const showTime = seoEnvMap && seoEnvMap.timeEnable && seoEnvMap.timeEnable.enable === false ? false : true;

	return (
		<section className="index-wrap-mobile">
			<HeaderMobile seoEnvMap={seoEnvMap} seoCategoryListData={seoCategoryListData} activeCategory={""} isShowSearch={true} />
			{
				seoFetchNewsLists && seoFetchNewsLists.length > 0 && (
					<ClassifiedNews lang={seoEnvMap.lang} item={seoFetchNewsLists} showTime={showTime} imgDomain={seoEnvMap.imgDomain} category="news" isShow={false} />
				)
			}

			{
				seoFetchNewsLists && seoFetchNewsLists.length > 4 && (
					<div className="scroll-wrap">
						<div className="scroll-item-title">Latest News</div>
						<div className="scroll-items">
							{
								seoFetchNewsLists.slice(4, seoFetchNewsLists.length).map(item => {
									return (
										<Scroll lang={seoEnvMap.lang} item={item} imgDomain={seoEnvMap.imgDomain} />
									)
								})
							}
						</div>
					</div>
				)
			}

			{
				seoCategoryDataList && seoCategoryDataList.length > 0 && seoCategoryDataList.slice(0, (seoCategoryDataList.length - 1)).map((item, index) => {
					if (index % 2 === 0) {
						return (
							<ClassifiedNews lang={seoEnvMap.lang} item={item.listData} showTime={showTime} imgDomain={seoEnvMap.imgDomain} category={item.category_name} isShow={true} />
						)
					} else {
						return (
							<div className="scroll-wrap">
								<div className="scroll-item-title">{item.category_name}</div>
								<div className="scroll-items">
									{
										item.listData && item.listData.length > 0 && item.listData.map(category => {
											return (
												<Scroll lang={seoEnvMap.lang} item={category} imgDomain={seoEnvMap.imgDomain} />
											)
										})
									}
								</div>
							</div>
						)


					}
				})
			}
		</section>
	);
}
