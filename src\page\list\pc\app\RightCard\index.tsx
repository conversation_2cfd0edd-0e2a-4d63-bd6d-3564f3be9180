import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import LazyImage from '@components/lazy-img';
import { appendCreativeIdToHref, getScaleImgSrc } from '@utils';
import { trackBiEvent, gtmEvent } from '@utils';
import { getFormattedDate } from '@utils';
import cls from 'classnames';
import { getLocale } from '@utils/locales';
interface IProps {
    seoNewsList: Array<any>;
    title: string;
    seoEnvMap: any;
}
export default function LeftCard(props: IProps) {
    const { seoNewsList, title, seoEnvMap } = props;
    const { imgDomain, lang, creativeId } = seoEnvMap;
    const onContentClick = (type: number, title: string, category: number) => {
        if (type === 2) return;
        const params = {
            event_name: 'news_click',
            page_id: category
        }
        trackBiEvent('news_click', params);

        // 发送gtm事件: 内容点击事件
        gtmEvent('item_click', {
            event_label: title
        });
    }
    return (
        <div className="right-card">
            <div className="title-warp">
                <div className="right-title">{title}</div>
            </div>
            <div className="content-warp">
                {
                    seoNewsList.map((item, index) => {
                        return (
                            <a
                                className={cls('item')}
                                onClick={() => { onContentClick(item.type, item.title, item.category[0]) }}
                                href={appendCreativeIdToHref(`/${lang}/${item.path || item.id}`, creativeId)}
                            >
                                <LazyImage className={cls('item-img')} src={getScaleImgSrc(item, imgDomain, '260*161')} alt={item.title} />
                                <div className={cls('item-content')}>

                                    <div className={cls('item-title')}>
                                        {item.title}
                                    </div>

                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="264" height="2" viewBox="0 0 264 2" fill="none">
                                    <path d="M0.5 1L263.5 1" stroke="black" stroke-opacity="0.3" />
                                </svg>
                            </a>
                        )

                    })
                }
            </div>
        </div>
    )
}
