import { IEnvMap, IPolicyAndContact } from "@types";
import "./style.less";
import HeaderPc from "@components/header-pc";
import { Footer, HeaderMobile } from "@components";
import { useEffect } from "preact/hooks";
import { adsComponentsInit } from "@utils";
import { getLocale } from "@utils/locales";

interface IProps {
	seoEnvMap: IEnvMap;
	policyAndContactRes: any;
	mainDomain: string;
	seoCategoryListData: Array<any>;
}

export default function App(props: IProps) {
	const { seoEnvMap, policyAndContactRes, seoCategoryListData, mainDomain } =
		props;
	const {
		creativeId,
		theme: { logo },
		isMobile,
		lang,
		siteId,
		timeEnable,
		hostname,
	} = seoEnvMap;
	const showTime = timeEnable && timeEnable.enable === false ? false : true;
	const { txt } = policyAndContactRes;
	useEffect(() => {
		adsComponentsInit();
	}, []);
	const renderContent = () => {
		return <div dangerouslySetInnerHTML={{ __html: txt }} />;
	};
	return (
		<section className="terms-text-info">
			{isMobile ? (
				<>
					<HeaderMobile seoEnvMap={seoEnvMap} seoCategoryListData={seoCategoryListData} activeCategory={""} isShowSearch={false} />
					<div className="terms-text-content">
						<div className="content-header">
							{getLocale(lang, "terms_of_use")}
						</div>
						{renderContent()}
					</div>
				</>
			) : (
				<>
					<HeaderPc
						seoCategoryListData={seoCategoryListData}
						seoEnvMap={seoEnvMap}
						activeCategory={seoCategoryListData[0]}
						creativeId={creativeId}


					/>
					<div className="terms-text-content">
						<div className="content-header">
							{getLocale(lang, "terms_of_use")}
						</div>
						{renderContent()}
					</div>
					<Footer

						creativeId={creativeId}
						lang={lang}
						logo={logo}
						hostname={hostname}

					/>
					{/* <Share seoEnvMap={seoEnvMap} /> */}
				</>
			)}

			{
				isMobile ? <Footer creativeId={seoEnvMap.creativeId} lang={seoEnvMap.lang} logo={seoEnvMap.theme.logo} hostname={seoEnvMap.hostname} isMobile={true} />
					: null
			}
		</section>
	);
}
