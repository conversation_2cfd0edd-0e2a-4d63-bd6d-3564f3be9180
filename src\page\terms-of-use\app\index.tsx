import { IEnvMap, IPolicyAndContact } from "@types";
import "./style.less";
import HeaderPc from "@components/header-pc";
import { Footer, HeaderMobile } from "@components";
import { useEffect } from "preact/hooks";
import { adsComponentsInit } from "@utils";
import { getLocale } from "@utils/locales";

interface IProps {
	seoEnvMap: IEnvMap;
	policyAndContactRes: any;
	mainDomain: string;
	seoCategoryListData: Array<string>;
}

export default function App(props: IProps) {
	const { seoEnvMap, policyAndContactRes, seoCategoryListData, mainDomain } =
		props;
	const {
		creativeId,
		theme: { logo },
		isMobile,
		lang,
		siteId,
		timeEnable,
		hostname,
	} = seoEnvMap;
	const showTime = timeEnable && timeEnable.enable === false ? false : true;
	const { txt } = policyAndContactRes;
	useEffect(() => {
		adsComponentsInit();
	}, []);
	const renderContent = () => {
		return <div dangerouslySetInnerHTML={{ __html: txt }} />;
	};
	return (
		<section className="terms-text-info">
			{isMobile ? (
				<>
					{/* <HeaderMobile
						seoEnvMap={seoEnvMap}
						seoCategoryListData={seoCategoryListData}
						seoActiveCategory={seoCategoryListData[0]}
						showTime={showTime}
						siteId={siteId}
					/>
					<div className="mobile-inner">
						<div className="content-mobile">
							<h3 className="content-header">
								{getLocale(lang, "terms_of_use")}
							</h3>
						</div>
						<div
							className="content-container"
							dangerouslySetInnerHTML={{ __html: txt }}
						/>
					</div> */}
				</>
			) : (
				<>
					<HeaderPc
						seoCategoryListData={seoCategoryListData}
						seoEnvMap={seoEnvMap}
						activeCategory={seoCategoryListData[0]}
						creativeId={creativeId}
						

					/>
					<div className="terms-text-content">
						<div className="content-header">
							{getLocale(lang, "terms_of_use")}
						</div>
						{renderContent()}
					</div>
					<Footer
						
						creativeId={creativeId}
						lang={lang}
						logo={logo}
						hostname={hostname}

					/>
					{/* <Share seoEnvMap={seoEnvMap} /> */}
				</>
			)}
		</section>
	);
}
