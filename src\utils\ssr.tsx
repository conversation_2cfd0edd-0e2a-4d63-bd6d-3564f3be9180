import renderToStaticMarkup from 'preact-render-to-string';
import { VERSION, BUILD_NAME } from '@constants';
import { isEmpty } from '@utils/index';
import { IF2eFiles } from '@types';

/**
 * 获取ssr的html，只抽取了部分代码为公共代码，原因是：App组件各不相同，且外层布局可能会存在变更的情况
 * @param env
 * @param module
 * @param title
 * @param content
 * @param props
 * @param f2eFiles
 */
export const getSsrHtml = (env: string, module: string,  content: string, props: any = {}, f2eFiles: IF2eFiles) => {
	const { seoEnvMap = {}, seoDetailsData } = props;
	const { siteId, adsTagHref, theme = {}, hostname, system, seoLan, title } = seoEnvMap;
	const { icon } = theme;

	let adsTagTitle = title;
	if (seoDetailsData && seoDetailsData.title) {
		adsTagTitle = seoDetailsData.title;
	}
	let keywords = title;
	let description = title;

	try {
		// seo: 游戏详情页增加资讯标题
		if (seoDetailsData && seoDetailsData.title) {
			keywords = seoDetailsData.title;
			description = `${seoDetailsData.title} | Maticoo News`;
		}
	} catch (e){

	}

	// 界面数据更新时需要React需要APP_PROPS来支持更新，比如: 响应式触发时布局的调整、图片大小的切换
	const propsScript = 'window.APP_PROPS = ' + JSON.stringify(props);

	// ssr服务端渲染需要使用到，正式环境中为空字符串
	const origin = process.env.ENTRY_ORIGIN;
	// 生成换肤的 root css
	const renderRootCss = () => {
		if (!isEmpty(theme)) {
			const { icon, title, logo, ...themeCss } = theme;
			const themeColorStr = Object.keys(themeCss)
				.map((key: string) => theme[key] === '' ? '' : `--${key}: ${theme[key]};`)
				.join('');
			return (
				<style dangerouslySetInnerHTML={{__html: `:root {${themeColorStr}}`}}/>
			);
		}
	}

	const renderScript = () => {
		// 本地环境，使用开发脚本
		if (env === 'development' || env === 'qa' || env === 'pro') {
			return (
				<>
					<script dangerouslySetInnerHTML={
						{ __html: f2eFiles.preactStr }
					}/>
					<script defer src={`/${BUILD_NAME}/${VERSION}/${module}.entry.js`}/>
				</>
			)
		}
		// 构建环境使用压缩版本
		return (
			<>
				<script dangerouslySetInnerHTML={
					{ __html: f2eFiles.preactStr }
				}/>
				<script dangerouslySetInnerHTML={{
					__html: f2eFiles.jsStr
				}}/>
			</>
		)
	}
	const renderCss = () => {
		// 本地环境，使用开发脚本
		if (env === 'development' || env === 'qa' || env === 'pro') {
			return (
				<link rel="stylesheet" type="text/css" href={`/${BUILD_NAME}/${VERSION}/${module}.entry.css`}/>
			)
		}
		return (
			<style dangerouslySetInnerHTML={{
				__html: f2eFiles.cssStr
			}}/>
		);
	}
	// GTM
	const renderGTM = () => {
		if (module === 'search-special') {
			return null;
		}
		if (module === 'coupon') {
			// 当是优惠券页面的时候，返回不同的gtmId
			return (
				<>
					<script dangerouslySetInnerHTML={
						{__html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5T38WZJC');`}
					}/>
				</>
			);
		}
		return (
			<>
				<script dangerouslySetInnerHTML={
					{ __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-WJ6SSRR');` }
				}/>

			</>
		);
	}

	// search-ads 脚本
	const renderGoogleScript = () => {
		return (
			<>
				<script async src="https://www.google.com/adsense/search/ads.js" />
				<script dangerouslySetInnerHTML={
					{ __html: `(function(g,o){g[o]=g[o]||function(){(g[o]['q']=g[o]['q']||[]).push( arguments)},g[o]['t']=1*new Date})(window,'_googCsa');` }
				}/>
			</>
		)
	}

	const renderFavicon = () => {
		let favicon = `${origin}/${BUILD_NAME}/site-icon.ico`;
		if (icon) {
			favicon = icon;
		}
		return favicon;
	}

	// ads-tag所需脚本和预加载脚本
	const renderAdsTag = () => {

		return (
			<>
				<script dangerouslySetInnerHTML={{
					__html: `window.adsTag = window.adsTag || { cmd: [] }; window.templateFlag = true;`
				}}/>
				<script defer id="ads-tag-sdk"  data-site-id={siteId} data-title={adsTagTitle} dangerouslySetInnerHTML={
					{ __html: f2eFiles.adsTagStr }
				}/>
				<script async id="gpt-sdk" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"/>
			</>
		);
	}

	// 需要加载搜索广告脚本的模块
	const searchModule = ['search-special', 'search-query', 'view-content-pc', 'view-content-mobile', 'landing-page'];// 'content-pc','content-mobile',

	let systemAttr: string = '';
	if (system.isPC) {
		systemAttr = 'pc';
	}
	if (system.isPhone || system.isAndroid) {
		systemAttr = 'mobile';
	}
	if (system.isTablet) {
		systemAttr = 'pc';  // ipad使用与pc同样的样式
	}

	return (
		renderToStaticMarkup(
			<html lang={seoLan}>
			<head>
				<meta charSet='UTF-8'/>
				<title>{title}</title>
				<link rel='preconnect' href='https://sdk.beesads.com'/>
				<link rel='preconnect' href='https://img.enjoy4fun.com'/>
				<link rel='preconnect' href='https://api.gamebridge.games'/>
				<link rel='preconnect' href='https://deapi.funsdata.com'/>
				{
					renderCss()
				}
				{
					renderRootCss()
				}
				<link type="image/x-icon" href={renderFavicon()} rel='shortcut icon'/>
				<link type="image/x-icon" href={renderFavicon()} rel='icon'/>
				<meta
					content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
					name='viewport'/>
				<meta name='keywords' content={keywords}/>
				<meta name='description' content={description}/>
				<meta name='google-site-verification' content='OlMueGMk6-1onaSMdJuqAYDjMJIhrvT_NaPrX6M9oos'/>
				<meta name='lhverifycode' content='32dc01246faccb7f5b3cad5016dd5033'/>
				<meta name='verify-admitad' content='4699800289'/>
				{searchModule.includes(module) ? renderGoogleScript() :
					<link href='https://fonts.font.im/css?family=Poppins:600' rel='stylesheet'/>}
				
				{
					renderAdsTag()
				}
			</head>
			<body data-system={systemAttr}>
			<section id="root" className={`${module}-page`} dangerouslySetInnerHTML={
				{ __html: content }
			} />
			<script dangerouslySetInnerHTML={
				{ __html: propsScript }
			}/>

			{
				renderScript()
			}
			{
				renderGTM()
			}
			</body>
			</html>
		)
	);
}
