.search-result-wrap-pc{
	width: 100%;
	background-color: var(--theme-bg-color);
	min-height: 100%;
	.search-page-warp {
	min-height:100vh;
	position: relative;

	.search-result-name {
		width: 100%;
		height: 49px;
		display: flex;
		align-items: center;
		margin: 0 auto;
		// margin-top: 84px;
		border-bottom: 1px solid #D9DCE2;
		background: color-mix(in srgb, #fff 80%, var(--theme-color) 20%);;
		box-shadow: 0px 2px 4px 0px rgba(190, 190, 190, 0.25);

		p {
			width: 1200px;
			margin: 0 auto;
			color: #000;
			font-family: Afacad;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
			text-transform: capitalize;
			display: flex;
			align-items: center;
			gap: 4px;

			.search-result-name-keyword {
				font-weight: 600;
			}
		}
	}

	.search-result-page-card-warp {
		width: 1200px;
		margin: 0 auto;
		margin-top:26px;
		display: flex;
		flex-direction: column;
		gap: 12px;
		padding-bottom:125px;
		
	}

	.search-result {
		display: flex;
		width: 100%;
		
	}

	.empty-warp {

		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8px;
		img {

			width: 130px;
			height: 130px;
		}

		.empty-desc {
			color: var(--theme-color);
			font-family: Inter;
			font-size: 24px;
			font-style: normal;
			font-weight: 800;
			line-height: normal;
			text-transform: capitalize;

		}
	}
}
}
