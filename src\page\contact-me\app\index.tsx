import { IEnvMap } from "@types";
import "./style.less";
import HeaderPc from "@components/header-pc";
import { Footer, HeaderMobile } from "@components";
import { useEffect } from "preact/hooks";
import { adsComponentsInit } from "@utils";
import { getLocale } from "@utils/locales";

interface IProps {
	seoEnvMap: IEnvMap;
	seoPolicyAndContact: any;
	mainDomain: string;
	seoCategoryListData: {
		language: string;
		name: string;
		id: number;
	}[];
}

export default function App(props: IProps) {
	const { seoEnvMap, seoPolicyAndContact, seoCategoryListData } = props;
	const {
		creativeId,
		theme: { logo },
		isMobile,
		lang,
		timeEnable,
		siteId,
		hostname,
	} = seoEnvMap;
	const { txt } = seoPolicyAndContact;
	const showTime = timeEnable && timeEnable.enable === false ? false : true;
	const renderContent = () => {
		return (
			<div
				style={{ paddingTop: 20, color: "#fb5092" }}
				dangerouslySetInnerHTML={{ __html: txt }}
			/>
		);
	};
	useEffect(() => {
		adsComponentsInit();
	}, []);
	return (
		<section>
			{isMobile ? (
				<>
					<HeaderMobile
						seoEnvMap={seoEnvMap}
						seoCategoryListData={seoCategoryListData}
						seoActiveCategory={""}
						showTime={showTime}
						siteId={siteId}
					/>
					<div className="mobile-inner">
						<div className="content-mobile">
							<h3 className="content-header">
								{getLocale(lang, "contact_us")}
							</h3>
						</div>
						<div className="content-container">
							{renderContent()}
						</div>
					</div>
				</>
			) : (
				<>
					<HeaderPc
						creativeId={creativeId} seoCategoryListData={seoCategoryListData} activeCategory={''} seoEnvMap={seoEnvMap} 
					/>
					<div className="contact-me-text-info">
						<div className="contact-title">
							{getLocale(lang, "contact_us")}
						</div>
						{renderContent()}
					</div>
					<Footer
						creativeId={creativeId}
						lang={lang}
						logo={logo}
						hostname={hostname}
					/>
				</>
			)}
		</section>
	);
}
