.right-card {
    display: flex;
    width: 292px;

    padding: 20px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .right-title {
        color: #000;
        font-family: Inter;
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        text-transform: capitalize;
    }
    .content-warp{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        align-self: stretch;
    }

    .item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        align-self: stretch;
        

        .item-img {
            width: 260px;
            height: 161px;
            align-self: stretch;
            border-radius: 10px;
            ;
        }

        .item-title {
            width: 263px;
            color: #000;
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            text-transform: capitalize;
        }
    }
}