import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import LazyImage from '@components/lazy-img';
import { appendCreativeIdToHref, getScaleImgSrc } from '@utils';
import { trackBiEvent, gtmEvent } from '@utils';
import cls from 'classnames';
interface IProps {
    seoNewsList: Array<any>;
    title: string;
    seoEnvMap: any;
}
export default function LastNews(props: IProps) {
    const { seoNewsList, title, seoEnvMap } = props;
    const { imgDomain, lang, creativeId } = seoEnvMap;
    const onContentClick = (type: number, title: string, category: number) => {
        if (type === 2) return;
        const params = {
            event_name: 'news_click',
            page_id: category
        }
        trackBiEvent('news_click', params);

        // 发送gtm事件: 内容点击事件
        gtmEvent('item_click', {
            event_label: title
        });
    }
    return (
        <div className="last-news">
            <div className="last-title">{title}</div>
            <div className="last-warp">
                {
                    seoNewsList.map((item, index) => {
                        return (
                            <a
                            className={cls('last-item')} 
                            onClick={() => { onContentClick(item.type, item.title, item.category[0]) }}
                                href={appendCreativeIdToHref(`/${lang}/${item.path || item.id}`, creativeId)}>
                                <LazyImage className={cls('item-img')} src={getScaleImgSrc(item, imgDomain, '267*150')} alt={item.title} />
                                <div className={cls('item-content')}>
                                    <div className={cls('item-title')}>
                                        {item.title}
                                    </div>
                                    <div className={cls('item-desc')}>
                                        {item.abstract}
                                    </div>
                                </div>
                            </a>
                        )
                    })
                }
            </div>
        </div>
    )
}
