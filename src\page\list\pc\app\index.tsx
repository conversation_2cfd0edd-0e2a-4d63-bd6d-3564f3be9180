import { useState, useEffect } from 'preact/hooks';
import { ContentCard, ContentList, ContentLoadMore, Footer, HeaderPc, PageHeader } from '@components'
import { adsComponentsInit, trackBiEvent, appendCreativeIdToHref } from '@utils';
import { fetchList } from '@stores';
import './style.less';
import { IContentList, IEnvMap, IContent } from '@types';
import { getLocale } from '@utils/locales';
import LastNews from './LastNews';
import LeftCard from './LeftCard';
import RightCard from './RightCard';
const adsMap: any = {};
interface IProps {
	seoCategory: string;
	seoCategoryListData: Array<any>;
	seoCategoryListItemData: Array<any>
	seoEnvMap: IEnvMap;
	seoCategoryDataList: Array<any>;
}
// 每次拉取条数
const PAGE_SIZE = 19;
let nowPage = 2;

export default function App(props: IProps) {
	const { seoCategory, seoCategoryListData, seoCategoryListItemData, seoEnvMap, seoCategoryDataList } = props;
	const { lang, siteId, creativeId, imgDomain, theme, timeEnable, hostname } = seoEnvMap;
	const { logo } = theme;
	const [recommend, setRecommend] = useState<any>([]);
	const showTime = timeEnable && timeEnable.enable === false ? false : true;
	const [list, setList] = useState<any>([]);
	const CategoryListItemData = seoCategoryListItemData.slice(4)
	const endIndex = 0;
	const [hasMore, setHasMore] = useState(true); // 是否有更多数据

	const chunkArray = (arr, size) => {
		const result = [];
		for (let i = 0; i < arr.length; i += size) {
			result.push(arr.slice(i, i + size));
		}
		return result;
	};
	useEffect(() => {
		adsComponentsInit();
	}, []);
	
	useEffect(() => {
		const targetIndex = seoCategoryDataList.findIndex(
			item => item.category_name.name === seoCategory
		);

		let finalRecommend = [];
		if (targetIndex !== -1 && seoCategoryDataList[targetIndex + 1]) {
			finalRecommend = seoCategoryDataList[targetIndex + 1].listData;
			setRecommend(finalRecommend); // 如果你后面还用 recommend 变量
		}
		if (targetIndex === seoCategoryDataList.length - 1) {
			finalRecommend = seoCategoryDataList[0].listData;
			setRecommend(finalRecommend);
		}

		const chunkArray = (arr, size) => {
			const result = [];
			for (let i = 0; i < arr.length; i += size) {
				result.push(arr.slice(i, i + size));
			}
			return result;
		};

		const groupedSeo = chunkArray(CategoryListItemData, 4);
		const groupedRecommend = chunkArray(finalRecommend, 3); // 使用上面刚刚拿到的值

		const maxLen = Math.max(groupedSeo.length, groupedRecommend.length);
		const mergedList = [];

		for (let i = 0; i < maxLen; i++) {
			mergedList.push([
				groupedSeo[i] || [],
				groupedRecommend[i] || []
			]);
		}
		// console.log(mergedList, 'mergedList');

		setList(mergedList);

	}, []);
	// console.log(list,'list');
	const loadMoreData = async () => {
		if (!hasMore) return;
		fetchList({
			page_index:nowPage,
			page_size:PAGE_SIZE,
			category:seoCategory,
			last_published_time:list[list.length - 1]?.[0]?.[0]?.published_time||'',
			last_id:list[list.length - 1]?.[0]?.[0]?.id||'',
		}).then((res)=>{
			const { data } = res;
			if (data.length < PAGE_SIZE) {
				setHasMore(false);
			}
			nowPage++;
			//data每四个一组，加入到list数组中
			const chunkedData = chunkArray(data, 4);
			setList(prevList => [...prevList, chunkedData]);
		})

	}
	// const loadMoreData = async () => {
	// 	// 1. 检查是否有更多数据或list是否为空
	// 	if (!hasMore || list.length === 0) return;

	// 	// 2. 安全获取最后一条数据
	// 	const lastGroup = list[list.length - 1];
	// 	const lastItem = lastGroup?.[0]?.[0]; // 使用可选链避免报错

	// 	// 3. 如果没有有效数据，使用默认值
	// 	const params = {
	// 		page_index: nowPage,
	// 		page_size: PAGE_SIZE,
	// 		category: seoCategory,
	// 		last_published_time: lastItem?.published_time || '', // 提供默认值
	// 		last_id: lastItem?.id || '' // 提供默认值
	// 	};

	// 	try {
	// 		const res = await fetchList(params);
	// 		const { data } = res;

	// 		if (data.length < PAGE_SIZE) {
	// 			setHasMore(false);
	// 		}

	// 		nowPage++;
	// 		const chunkedData = chunkArray(data, 4);
	// 		setList(prevList => [...prevList, ...chunkedData]);
	// 	} catch (error) {
	// 		console.error('加载更多数据失败:', error);
	// 	}
	// }

	return (
		<section className="list-wrap">
			<HeaderPc seoEnvMap={seoEnvMap} logo={logo} creativeId={creativeId} seoCategoryListData={seoCategoryListData} />
			<div className="list-card">
				<div className="header-title">
					<div className="title-box">
						<a
							href={appendCreativeIdToHref(`/${lang}/`, creativeId)}
							className="home"
						>
							{getLocale(lang, "home")}
						</a>
						/<span className="category">{seoCategory}</span>
					</div>
				</div>
				<div className="list-content">
					<div>
						<LastNews seoEnvMap={seoEnvMap} seoNewsList={seoCategoryListItemData.slice(0, 4)} title={getLocale(lang, 'latest news')} />
					</div>


					<div className="list-result" >
						<div className="left-list">
							{list.map((item, index) => {
								return (item[0]?.length > 0 && <LeftCard categoryTitle={seoCategory} title={index === 0 ? seoCategory : undefined} seoEnvMap={seoEnvMap} seoNewsList={item[0]} />)
							})
							}
						</div>
						<div className="right-list">
							{recommend?.length > 0 && <RightCard title={getLocale(lang, 'recommend')} seoEnvMap={seoEnvMap} seoNewsList={recommend} />}

						</div>
					</div>

				</div>
			</div>
			<ContentLoadMore seoEnvMap={seoEnvMap} loadMore={loadMoreData} hasMore={hasMore} />
			<Footer creativeId={creativeId} lang={lang} logo={logo} hostname={hostname} />

		</section>
	);
}
