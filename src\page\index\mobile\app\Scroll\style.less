.scroll-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    width: 267px;
    height: 243px;
    flex-shrink: 0;

    img {
        width: 100%;
        aspect-ratio: 1.78 / 1;
        object-fit: cover;
        border-radius: 8px 8px 0px 0px;
    }

    .scroll-item-info {
        border-radius: 8px;
        background: #FFF;
        display: flex;
        width: 100%;
        padding: 2px 0px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        transform: translateY(-12px);
        justify-content: center;
        height: 93px;

        .title {
            color: #080F18;
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 25px;
            /* 156.25% */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            overflow: hidden;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            text-transform: capitalize;
            padding: 0 8px;
        }

        .title-active{
            -webkit-line-clamp: 1;
        }

        .desc {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            overflow: hidden;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            text-transform: capitalize;
            padding: 0 8px;
            color: #646464;
            font-family: Inter;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            width: 100%;
        }
    }
}