 .bottom-item {
     display: flex;
     flex-direction: column;
     justify-content: center;
     align-items: flex-start;
     width: 100%;
     height: 195px;
     position: relative;
     margin-bottom: 8px;

     img {
         object-fit: cover;
         height: 167px;
         aspect-ratio: 1.95 / 1;
         border-radius: 8px;
         flex-shrink: 0;
     }

     .bottom-item-info {
         border-radius: 8px;
         background: #FFF;
         display: flex;
         width: 90%;
         padding: 12px 16px;
         flex-direction: column;
         align-items: flex-start;
         gap: 16px;
         height: 109px;
         // transform: translate(51px,-94px);
         position: absolute;
         left: calc(100% - 70% - 14.8%);
         top: 94px;

         .title {
             color: #28252C;
             font-family: Inter;
             font-size: 14px;
             font-style: normal;
             font-weight: 500;
             line-height: normal;
             width: 100%;
             text-transform: capitalize;

             .title-category {
                 font-size: 15px;
                 font-weight: 600;
             }
         }

         .desc {
             color: #28252C;
             font-family: Inter;
             font-size: 16px;
             font-style: normal;
             font-weight: 600;
             line-height: normal;
             width: 100%;
             overflow: hidden;
             text-overflow: ellipsis;
             display: -webkit-box;
             -webkit-line-clamp: 2;
             overflow: hidden;
             /*! autoprefixer: off */
             -webkit-box-orient: vertical;
             text-transform: capitalize;
         }
     }
 }
.bottom-margin{
    margin-top: 12px;
}

 .category-item {
     margin-top: 22px;

     .category-item-content {
         width: 83% !important;
         left: calc(100% - 70% - 7.8%);
         right: unset !important;
     }
 }


 .content-title-time{
        -webkit-line-clamp: 1 !important;
 }