import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import { LazyImg, LinkOrA } from '@components';
import { getFormattedDate, getFormattedNumberDate, getScaleImgSrc } from '@utils';
import { IContent } from '@types';
import cls from 'classnames';

interface IProps {
  item: Array<IContent>
  lang: string
  imgDomain: string
  category: string
}
export default function BottomNews(props: IProps) {
  const { item, lang, imgDomain, category } = props;
  return (
    <div className="bottom-news">
      <div className="name">{category}</div>
      <div className="list">
        {
          item && item.map(bottomNews => {
            return (
              <LinkOrA path={`/${lang}/${bottomNews.path || bottomNews.id}`} className="bottom-item">
                <LazyImg src={getScaleImgSrc(bottomNews, imgDomain, '534*300')} alt={bottomNews.title} />
                <div className="bottom-item-info">
                  <div className={cls("title")}><span>{category}</span> - {getFormattedDate(bottomNews.published_time)}</div>
                  <div className="desc">{bottomNews.title}</div>
                </div>
              </LinkOrA>
            )
          })
        }
      </div>
    </div>
  )
}
