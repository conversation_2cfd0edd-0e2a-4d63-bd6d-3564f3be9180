@import "../../../assets/css/config.less";

body {
	margin: 0 auto;
	width: 100%;
	.text-info {
		position: relative;
		min-height: 100%;
		max-width: 896px;
		margin: 0 auto;
		margin-top: var(--header_height);
		margin-bottom: 45px;

		.text-content {
			font-weight: 400;

			.content-header {
				font-size: 36px;
				font-weight: 800;
				padding: 45px 0 32px 0;
				text-transform: capitalize;
			}

			.content-inner {
				position: relative;
				z-index: 999;
				pointer-events: auto;
			}
			a {
				color: var(--text-color);
			}

			h3 {
				font-weight: 600;
				font-size: 20px;
				line-height: 24px;
				margin-bottom: 10px;
			}

			h4 {
				font-weight: 600;
				font-size: 16px;
				line-height: 20px;
				margin-bottom: 10px;
			}

			div {
				// margin: 29px 75px;
				display: flex;
				flex-direction: column;
				gap: 18px;
			}

			p,
			li {
				font-size: 16px;
				font-weight: 400;
				line-height: 1.5;
				text-transform: capitalize;
			}
			table {
				table-layout: fixed;
				width: 100% !important;
				border: 1px solid var(--theme-color);
				overflow-x: auto;
				margin-bottom: 34px;
				border-collapse: collapse;
				border-spacing: 0;

				td {
					padding: 12px 20px;
					line-height: 24px;
					font-size: 14px;
					color: black;
					border: 1px solid var(--theme-color);
				}
			}
		}
	}
	// .mobile-text{
	//   .mobile-title{
	//     font-size: 24px;
	//     font-weight: 700;
	//     padding: 10px 16px 10px 15px;
	//   }
	//   .mobile-text{
	//     font-size: 16px;
	//     margin: 0 16px;
	//     p,
	// 				span,
	// 				ul,
	// 				li {
	// 					font-size: 16px;
	// 					font-weight: 400;
	// 					line-height: 1.8;
	// 				}
	//     h1 {
	//       font-weight: 800;
	//       font-size: 20px;
	//     }

	//     h2 {
	//       font-weight: 700;
	//       font-size: 18px;
	//     }

	//     h3 {
	//       font-weight: 600;
	//       font-size: 17px;
	//     }
	//     .ad-wrap {
	//       margin: 10px 0;
	//     }
	//     h1,
	//     h2,
	//     h3,
	//     h4,
	//     h5,
	//     h6,
	//     hr,
	//     p,
	//     blockquote,
	//     dl,
	//     dt,
	//     dd,
	//     ul,
	//     ol,
	//     li,
	//     pre,
	//     fieldset,
	//     button,
	//     input,
	//     textarea,
	//     th,
	//     td {
	//       margin: revert;
	//       padding: revert;
	//     }
	//   }
	// }
}
body[data-system="mobile"] {
	.privacy-policy-page {
		padding-top: 84px;
		.text-info {
			margin: 0;
			.content-mobile {
				color: #000;
				font-size: 18px;
				font-weight: 700;
				height: 58px;
				line-height: 58px;
				margin: 0 auto;
				text-align: center;
				width: 100%;
			}
		}
		.text-content {
			padding: 0 20px;
		}
	}
}
