import { renderToString } from 'preact-render-to-string';
import App from './app';
import { getSsrHtml } from '@utils/ssr';
import { IContent, IEnvMap, IF2eFiles } from '@types';

interface IProps {
	seoDetailsData: IContent;
	seoEnvMap: IEnvMap;
}
export default function SSR(env: string, props: IProps, f2eFiles: IF2eFiles) {
	const content = renderToString(
		<App {...props}/>
	);
	return getSsrHtml(env, 'content-mobile', content, props, f2eFiles);
}
