import { IContent, IContentList, IRenderAdsConfig } from '@types';
import { useEffect, useState } from 'preact/hooks';
import { Fragment } from 'preact';
import { fetchList } from '@stores';
import { ContentCard, Loading } from '@components';
import './style.less';

// 每次拉取条数
const PAGE_SIZE = 10;
let nowPage = 1;
let nowCategory: string;

// 是否启用拉取
let useFetch = false;
let nowList: Array<IContent>;
const adsMap: any = {};
const recommendAds = {
	zone_key: '728x90_pc_index_list',
	width: 728,
	height: 90
}

interface IProps {
	listData: Array<IContent>;
	category: string;
	renderAds?: IRenderAdsConfig;
	zoneMap?: any,
	isMobile?: boolean,
	imgDomain: string,
	siteId: string,
	creativeId: string
}

export default function ContentListPc(props: IProps) {
	const { listData, category, zoneMap, isMobile, imgDomain, siteId, creativeId } = props;
	const [list, setList] = useState(listData);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		nowPage = 1;
		nowList = listData;
		nowCategory = category;
		setList(listData);
	}, [listData]);
	useEffect(() => {
		window.addEventListener('scroll', function () {
			if (!useFetch && window.innerHeight + window.scrollY + 100 > document.body.offsetHeight) {
				nowPage = nowPage + 1;
				useFetch = true;
				setLoading(true);
				fetchList({
					page_size: PAGE_SIZE,
					page_index: nowPage,
					category: nowCategory,
					last_published_time: nowList[nowList.length - 1]?.published_time,
					last_id: nowList[nowList.length - 1]?.id,
					site_id: siteId
				}).then((res: IContentList) => {
					const { data } = res;
					nowList = nowList.concat(data)
					setList(nowList);
					// 当存在数据 且 当前不为最后一页
					if (data && data.length === PAGE_SIZE) {
						useFetch = false;
					}
				}).finally(() => {
					setLoading(false);
				});
			}
		});
	}, []);

	const listRef = (node: any) => {
		if (node) {
			Object.keys(adsMap).forEach(adsId => {
				const { width, height, zoneId } = adsMap[adsId];
				const dom = document.querySelector(`[data-ads-id="${adsId}"]`);
				if (dom && !dom.innerHTML) {
					window.adsTag.cmd.push(() => {
						window.adsTag.renderAds(dom, width, height, zoneId);
					})
				}
			});
		}
	}

	if (!list || !list.length) {
		return null;
	}

	const renderAdsDom = (renderAds:IRenderAdsConfig, index: number) => {
		const { zone_key, width, height } = renderAds;
		const adsId = `ads-dom-${zone_key}_${index}`;
		if(!adsMap[adsId]) {
			adsMap[adsId] = {
				width,
				height,
				zoneId: zoneMap[zone_key],
			};
		}
		return (
			<div className="ad-wrap" data-ads-id={adsId}></div>
		);
	}

	const renderList = (arrayList: Array<IContent>, index: number) => {
		return (
			<Fragment key={index}>
				<div className="row-content">
					{ arrayList[0] ? <ContentCard content={arrayList[0]} bigCard category={category} isMobile={isMobile} imgDomain={imgDomain} isLazy={nowPage > 1} creativeId={creativeId}/> : null}
					{ arrayList[1] ? <ContentCard content={arrayList[1]} bigCard category={category} isMobile={isMobile} imgDomain={imgDomain} isLazy={nowPage > 1} creativeId={creativeId}/> : null}
				</div>
				{
					arrayList.map((item, index) => {
						if (index % 10 === 0 || index % 10 === 1) {
							return
						}
						return (
							<Fragment key={item.id}>
								<ContentCard content={item} category={category} isMobile={isMobile} imgDomain={imgDomain} isLazy={nowPage > 1} creativeId={creativeId}/>
							</Fragment>
						)
					})
				}
				{
					arrayList.length === 6 ? (
						<div className="ad-wrap-middle">
							<div style={{ width: '728px' }}>
								{
									renderAdsDom(recommendAds, index)
								}
							</div>
						</div>
					) : null
				}
			</Fragment>
		)
	}

	const renderListData = (list:Array<IContent>) => {
		const listData = splitArray(list);
		return (
			listData.map((item,index) => {
				return renderList(item, index)
			})
		)
	}

	const splitArray = (arrayList:Array<IContent>) => {
		const resultArray = [];
		for (let i = 0; i < arrayList.length; i += 6) {
			resultArray.push(arrayList.slice(i, i + 6));
		}
		return resultArray;
	}

	return (
		<div className="content-list-pc" ref={listRef}>
			{renderListData(list)}
			<Loading loading={loading}/>
		</div>
	);
}
