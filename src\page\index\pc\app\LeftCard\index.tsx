import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import LazyImage from '@components/lazy-img';
import { appendCreativeIdToHref, getScaleImgSrc } from '@utils';
import { trackBiEvent, gtmEvent } from '@utils';
import { getFormattedDate } from '@utils';
import cls from 'classnames';
import { getLocale } from '@utils/locales';
interface IProps {
    seoNewsList: Array<any>;
    title: string;
    seoEnvMap: any;
}
export default function LeftCard(props: IProps) {
    const { seoNewsList, title, seoEnvMap } = props;
    const { imgDomain, lang, creativeId, timeEnable } = seoEnvMap;
    const showTime = timeEnable && timeEnable.enable === false ? false : true;
    const onContentClick = (type: number, title: string, category: number) => {
        if (type === 2) return;
        const params = {
            event_name: 'news_click',
            page_id: category
        }
        trackBiEvent('news_click', params);

        // 发送gtm事件: 内容点击事件
        gtmEvent('item_click', {
            event_label: title
        });
    }
    return (
        <div className="left-card">
            <div className="title-warp">
                <div className="left-title">{title}</div>
                <a className="left-see-more" href={appendCreativeIdToHref(`/${lang}/list/${title}`, creativeId)}>
                    {getLocale(lang, 'see_more')}
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
                        <path d="M5 11.5L9 7.5L5 3.5" stroke="#244B9C" stroke-width="1.152" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </a>
            </div>
            <div className="content-warp">
                {
                    seoNewsList.map((item, index) => {
                        if (index === 0) {
                            return (
                                <a
                                    className={cls('item', 'first-item')}
                                    onClick={() => { onContentClick(item.type, item.title, item.category[0]) }}
                                    href={appendCreativeIdToHref(`/${lang}/${item.path || item.id}`, creativeId)}
                                >
                                    <LazyImage className={cls('first-item-img')} src={getScaleImgSrc(item, imgDomain, '461*300')} alt={item.title} />
                                    <div className={cls('first-item-content')}>
                                        <div className="first-item-label">
                                            {title}  {showTime && <span className="first-item-time">{`|`+getFormattedDate(item.published_time)}</span>}
                                        </div>
                                        <div className={cls('first-item-title')}>
                                            {item.title}
                                        </div>
                                        {item.abstract !== undefined && (
                                            <div className={cls('first-item-desc')}>
                                                {item.abstract}
                                            </div>
                                        )}
                                    </div>
                                </a>
                            )
                        } else {
                            return (
                                <a
                                    className={cls('item')}
                                    onClick={() => { onContentClick(item.type, item.title, item.category[0]) }}
                                    href={appendCreativeIdToHref(`/${lang}/${item.path || item.id}`, creativeId)}
                                >
                                    <LazyImage className={cls('item-img')} src={getScaleImgSrc(item, imgDomain, '267*157')} alt={item.title} />
                                    <div className={cls('item-content')}>

                                        <div className={cls('item-title')}>
                                            {item.title}
                                        </div>
                                        {item.abstract !== undefined && (
                                            <div className={cls('item-desc')}>
                                                {item.abstract}
                                            </div>
                                        )}
                                        {showTime && <div className="item-time">
                                            {getFormattedDate(item.published_time)}
                                        </div>}
                                    </div>
                                </a>
                            )
                        }

                    })
                }
            </div>
        </div>
    )
}
