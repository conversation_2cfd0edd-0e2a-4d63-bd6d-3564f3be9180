import { useEffect, useState } from 'preact/hooks';
import { ContentCard, Loading } from '@components';
import { Fragment } from 'preact';
import { fetchList } from '@stores';
import './style.less';
import { IContent, IContentList, IRenderAdsConfig } from '@types';

interface IProps {
	listData: Array<IContent>;
	category: string;
	renderAds?: Array<IRenderAdsConfig>;
	zoneMap?: any,
	isMobile?: boolean,
	imgDomain: string,
	siteId: string,
	creativeId: string
}

// 每次拉取条数
const PAGE_SIZE = 10;
let nowPage = 1;
let nowCategory: string;

// 是否启用拉取
let useFetch = false;
let nowList: Array<IContent>;
const adsMap: any = {};
export default function ContentListMobile(props: IProps) {
	const { listData, category, renderAds, zoneMap, isMobile, imgDomain, siteId, creativeId } = props;
	const [list, setList] = useState(listData);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		nowPage = 1;
		nowList = listData;
		nowCategory = category;
		setList(listData);
	}, [listData]);
	useEffect(() => {
		window.addEventListener('scroll', function () {
			if (!useFetch && window.innerHeight + window.scrollY + 100 > document.body.offsetHeight) {
				nowPage = nowPage + 1;
				useFetch = true;
				setLoading(true);
				fetchList({
					page_size: PAGE_SIZE,
					page_index: nowPage,
					category: nowCategory,
					last_published_time: nowList[nowList.length - 1]?.published_time,
					last_id: nowList[nowList.length - 1]?.id,
					site_id: siteId
				}).then((res: IContentList) => {
					const { data } = res;
					nowList = nowList.concat(data)
					setList(nowList);
					// 当存在数据 且 当前不为最后一页
					if (data && data.length === PAGE_SIZE) {
						useFetch = false;
					}
				}).finally(() => {
					setLoading(false);
				});
			}
		});
	}, []);

	const listRef = (node: any) => {
		if (node) {
			Object.keys(adsMap).forEach(adsId => {
				const { width, height, zoneId } = adsMap[adsId];
				const dom = document.querySelector(`[data-ads-id="${adsId}"]`);
				if (dom && !dom.innerHTML) {
					window.adsTag.cmd.push(() => {
						window.adsTag.renderAds(dom, width, height, zoneId);
					})
				}
			});
		}
	}

	if (!list || !list.length) {
		return null;
	}

	const renderAdsDom = (renderAds:IRenderAdsConfig, index: number) => {
		const { zone_key, width, height } = renderAds;
		const adsId = `ads-dom-${zone_key}_${index}`;
		if(!adsMap[adsId]) {
			adsMap[adsId] = {
				width,
				height,
				zoneId: zoneMap[zone_key],
			};
		}
		return (
			<div className="ad-wrap" data-ads-id={adsId}></div>
		);
	}

	const renderList = (arrayList: Array<IContent>, index: number) => {
		return (
			<Fragment key={index}>
				{
					arrayList.map((item: IContent) => {
						return (
							<ContentCard content={item} key={item.id} category={category} isMobile={isMobile} imgDomain={imgDomain} isLazy={nowPage > 1} creativeId={creativeId}/>
						)
					})
				}
				{
					renderAds && arrayList.length === 4 ? (
						<div>
							{
								renderAdsDom(renderAds[0], index)
							}
						</div>
					) : null
				}
			</Fragment>
		)
	}

	const renderListData = (list:Array<IContent>) => {
		const listData = splitArray(list);
		return (
			listData.map((item,index) => {
				return renderList(item, index)
			})
		)
	}

	const splitArray = (arrayList:Array<IContent>) => {
		const resultArray = [];
		for (let i = 0; i < arrayList.length; i += 4) {
			resultArray.push(arrayList.slice(i, i + 4));
		}
		return resultArray;
	}

	return (
		<div className="content-list" ref={listRef}>
			{
				renderListData(list)
			}
			<Loading loading={loading}/>
		</div>
	);
}
