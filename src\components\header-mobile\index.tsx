import { useState } from 'preact/hooks';
import './style.less';
import { IEnvMap } from '@types';
import SearchPng from '@assets/images/charm_search.png'
import { appendCreativeIdToHref } from '@utils';
import defaultLogo from "@assets/images/logo.svg"
import cls from 'classnames';
import Language from '@components/language';
import { getLocale } from '@utils/locales';
import { name } from 'webpack-ssr-node.config';
import LinkOrA from '@components/link-or-a';



interface IProps {
	seoEnvMap: IEnvMap;
	seoCategoryListData: Array<any>;
	searchResult?: string;
	activeCategory: string,
	isShowSearch: boolean
}

export default function HeaderMobile(props: IProps) {
	const { seoEnvMap, seoCategoryListData, searchResult, activeCategory, isShowSearch } = props;
	const [searchValue, setSearchValue] = useState(searchResult || '');
	const { theme, creativeId, lang, languageList, imgDomain } = seoEnvMap;
	const { logo } = theme || {};
	const [showInput, setShowInput] = useState(false);
	const [showSearch, setShowSearch] = useState(true);
	const [inputValue, setInputValue] = useState(null);
	const [showLang, setShowLang] = useState(false);

	const saveValue = (e: String) => {
		setInputValue(e)
		// console.log(e);

	}
	const showSearchResult = () => {
		if (inputValue) {
			window.location.href = `/${lang}/search-result?search_result=${inputValue}`
		}
	}
	const inputKeyDown = (e: any) => {
		if (inputValue && e.key === 'Enter') {
			window.location.href = `/${lang}/search-result?search_result=${inputValue}`
		}
	}
	return (
		<div className={cls("header-component-mobile", showLang ? "lang-active" : "")}>
			{
				showLang ? (
					<div className="language">
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className="language-close" onClick={() => { setShowLang(!showLang) }}>
							<path d="M13.5909 12L18.0441 7.54687C18.2554 7.3359 18.3743 7.04962 18.3745 6.75099C18.3748 6.45237 18.2564 6.16587 18.0455 5.95453C17.8345 5.74319 17.5482 5.62431 17.2496 5.62404C16.951 5.62378 16.6645 5.74215 16.4531 5.95312L12 10.4062L7.54687 5.95312C7.33553 5.74178 7.04888 5.62305 6.75 5.62305C6.45111 5.62305 6.16447 5.74178 5.95312 5.95312C5.74178 6.16447 5.62305 6.45111 5.62305 6.75C5.62305 7.04888 5.74178 7.33553 5.95312 7.54687L10.4062 12L5.95312 16.4531C5.74178 16.6645 5.62305 16.9511 5.62305 17.25C5.62305 17.5489 5.74178 17.8355 5.95312 18.0469C6.16447 18.2582 6.45111 18.3769 6.75 18.3769C7.04888 18.3769 7.33553 18.2582 7.54687 18.0469L12 13.5937L16.4531 18.0469C16.6645 18.2582 16.9511 18.3769 17.25 18.3769C17.5489 18.3769 17.8355 18.2582 18.0469 18.0469C18.2582 17.8355 18.3769 17.5489 18.3769 17.25C18.3769 16.9511 18.2582 16.6645 18.0469 16.4531L13.5909 12Z" fill="black" />
						</svg>
						<div className="language-title">{getLocale(lang, "change_language")}</div>
						<div className="language-list">
							{languageList.map((item) => {
								return (
									<a
										key={item}
										href={`/${item.code}`}
										className={cls(
											"language-item",
											seoEnvMap.isMobile ? "width-item" : "",
											item.code === lang ? "active" : ""
										)}
									>
										{/* <img
											src={`${imgDomain}/image/36*36/${item.icon}`}
											width={36}
											height={36}
											alt={item.name}
										/> */}
										<h3
											className={cls("language-item-name")}
										>
											{item.name}
										</h3>

									</a>
								);
							})}
						</div>
					</div>

				) : (
					<>
						<div className="header-content">
							<a className="title" href={appendCreativeIdToHref(`/${lang}`, creativeId)} title="ContentSiteV21" style={{ backgroundImage: logo ? `url(${logo})` : `url(${defaultLogo})` }} />

							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className="lan-icon" onClick={() => { setShowLang(!showLang) }} >
								<path d="M2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2C6.477 2 2 6.477 2 12Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M12.9999 2.05C12.9999 2.05 15.9999 6 15.9999 12C15.9999 18 12.9999 21.95 12.9999 21.95M10.9999 21.95C10.9999 21.95 7.99988 18 7.99988 12C7.99988 6 10.9999 2.05 10.9999 2.05M2.62988 15.5H21.3699M2.62988 8.5H21.3699" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
						</div>
						{
							isShowSearch && <div className='header-content-input'>
								<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className="input-search" onClick={showSearchResult} >
									<path fill-rule="evenodd" clip-rule="evenodd" d="M6.68108 15.433C5.4341 13.8254 4.84641 11.803 5.03758 9.77746C5.22874 7.75189 6.1844 5.87522 7.71013 4.52925C9.23586 3.18328 11.217 2.46912 13.2506 2.53205C15.2842 2.59497 17.2175 3.43027 18.6571 4.868C20.0979 6.30672 20.936 8.24072 21.0004 10.2759C21.0648 12.311 20.3508 14.2941 19.0039 15.8211C17.6569 17.3481 15.7784 18.304 13.7511 18.4941C11.7238 18.6841 9.70038 18.094 8.09308 16.844L8.05008 16.889L3.80808 21.132C3.71517 21.2249 3.60487 21.2986 3.48348 21.3489C3.36209 21.3992 3.23198 21.4251 3.10058 21.4251C2.96919 21.4251 2.83908 21.3992 2.71769 21.3489C2.59629 21.2986 2.48599 21.2249 2.39308 21.132C2.30017 21.0391 2.22647 20.9288 2.17619 20.8074C2.12591 20.686 2.10003 20.5559 2.10003 20.4245C2.10003 20.2931 2.12591 20.163 2.17619 20.0416C2.22647 19.9202 2.30017 19.8099 2.39308 19.717L6.63608 15.475L6.68108 15.433ZM8.75708 6.283C8.19248 6.83848 7.74345 7.50025 7.43588 8.23013C7.12832 8.96002 6.96831 9.74358 6.96508 10.5356C6.96185 11.3277 7.11548 12.1125 7.41709 12.8449C7.7187 13.5772 8.16233 14.2426 8.72239 14.8027C9.28245 15.3627 9.94785 15.8064 10.6802 16.108C11.4126 16.4096 12.1974 16.5632 12.9895 16.56C13.7815 16.5568 14.5651 16.3968 15.2949 16.0892C16.0248 15.7816 16.6866 15.3326 17.2421 14.768C18.3522 13.6397 18.9714 12.1184 18.965 10.5356C18.9585 8.9528 18.3269 7.43664 17.2077 6.31741C16.0884 5.19817 14.5723 4.56654 12.9895 4.5601C11.4066 4.55365 9.88539 5.17292 8.75708 6.283Z" fill="#C2C4CA" />
								</svg>
								<input
									type="text"
									value={inputValue}
									className='header-input'
									onChange={(e) => saveValue(e.target.value)}
									onKeyDown={inputKeyDown}
								/>
							</div>
						}
						{
							seoCategoryListData.length > 0 && seoCategoryListData &&
							<div className={cls("header-content-category")}>
								{
									seoCategoryListData.map(item => {
										return (
											<LinkOrA path={`/${lang}/list/${item.name}`} className={cls("category", activeCategory === item.name ? "category-active" : "")}>{item.name}</LinkOrA>
										)
									})
								}
							</div>
						}
					</>
				)
			}

		</div>


	)
}
