// 通用背景
@page-bg: #fff;

// 主题默认色
:root {
	// 背景颜色
	--bg_color: #FFF;
	// 目前分类的文字颜色
	--current_category_color: #000;
	// Card的文字颜色
	--card_title_text_color: #000;
	// 时间线颜色
	--timeline_color: #666;
	// 分割线颜色
	--border_color: #DBDADA;


	// pc端header高度
	--header_height: 84px;
	// pc端header背景色
	--header_current_category_color: #000;
	// pc端header背景色
	--header_bg_color: #FFF;
	// pc端header 分类 选中 文字颜色
	--header_li_color: #C0BEC3;
	// pc端header 分类 选中 背景颜色
	--header_li_active_color: #FF2727;

	// PC 咨询card 背景颜色

	--theme-pc-content-card-bg: #fff;

	// 页面主题背景色
	--theme-page-color: #F4F4F4;

	// m端页面主题颜色
	--theme-mobile-page-color: #fff;

	// 页面主题背景色
	--theme-color: #244B9C;
	--theme-bg-color: color-mix(in srgb, #FFFFFF 90%, var(--theme-color) 10%);

	// 主题圆角
	--theme-border-radius: 12px;

	// 首页主题7 hover 颜色
	--index-a-hover-text-color: #2fa1b3;
	--theme-seven-index-color: #db562b;
	--right-category-bg-color: #eef8f9;
}

//内容站主题色
//:root {
//	// 背景颜色
//	--bg_color: #17171B;
//	// 目前分类的文字颜色
//	--current_category_color: #FFF;
//	// Card的文字颜色
//	--card_title_text_color: rgba(255, 255, 255, 0.90);
//	// 时间线颜色
//	--timeline_color: rgba(255, 255, 255, 0.40);
//	// 分割线颜色
//	--border_color: rgba(236, 236, 236, 0.20);
//	// footer border颜色
//	--footer_border_color: rgba(7, 7, 7);
//}