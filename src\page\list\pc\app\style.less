.list-wrap {
	width: 100%;
	min-height: 100%;
	background-color: var(--theme-bg-color);

	.list-card {
		// margin-top: 84px;
		min-height: calc(100% - 222px);

	}

	.header-title {
		width: 100%;
		height: 49px;
		display: flex;
		align-items: center;
		gap: 4px;
		border-bottom: 1px solid #D9DCE2;
		background: color-mix(in srgb, #fff 80%, var(--theme-color) 20%);
		box-shadow: 0px 2px 4px 0px rgba(190, 190, 190, 0.25);

		.title-box {
			width: 1200px;
			margin: 0 auto;
			font-size: 14px;
			font-style: normal;
			line-height: normal;
			text-transform: capitalize;
			display: flex;
			color: #000;

			font-family: Inter;
			gap: 4px;

			.home {
				font-weight: 400;
				color: #000;
			}

			.category {
				font-weight: 700;
			}
		}
	}

	.list-content {
		display: flex;
		width: 1200px;
		flex-direction: column;
		align-items: flex-start;
		gap: 24px;
		margin: 0 auto;
		margin-top: 24px;
		padding-bottom: 24px;
	}

	.list-result {
		display: flex;
		gap: 16px;
		width: 100%;
		/* 确保占据全部宽度 */
		justify-content: flex-end;

		/* 默认右对齐 */
		&:has(.left-list:not(:empty)) {
			justify-content: space-between;
			/* 左右分布 */
		}
	}

	.left-list {
		display: flex;
		flex-direction: column;
		gap: 24px;
	}
}