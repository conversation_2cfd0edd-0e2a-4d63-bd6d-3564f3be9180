.list-wrap {
	width: 100%;
	min-height: 100%;
	background-color: var(--theme-bg-color);
	.list-card {
		margin-top: 84px;
		
	}

	.header-title {
		width: 100%;
		height: 49px;
		display: flex;
		align-items: center;
		gap: 4px;
		border-bottom: 1px solid #D9DCE2;
		background: linear-gradient(0deg, rgba(36, 75, 156, 0.20) 0%, rgba(36, 75, 156, 0.20) 100%), #FFF;
		box-shadow: 0px 2px 4px 0px rgba(190, 190, 190, 0.25);

		.title-box {
			width: 1200px;
			margin: 0 auto;
			font-size: 14px;
			font-style: normal;
			line-height: normal;
			text-transform: capitalize;
			display: flex;
			color: #000;

			font-family: Inter;
			gap: 4px;

			.home {
				font-weight: 400;
				color: #000;
			}

			.category {
				font-weight: 700;
			}
		}
	}

	.list-content {
		display: flex;
		width: 1200px;
		flex-direction: column;
		align-items: flex-start;
		gap: 24px;
		margin: 0 auto;
		margin-top:24px;
		padding-bottom: 24px;
	}
	.list-result{
		display: flex;
		gap:16px;
	}
	.left-list{
		display: flex;
		flex-direction: column;
		gap:24px;
	}
}