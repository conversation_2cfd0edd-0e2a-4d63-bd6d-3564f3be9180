.page-header {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.page-header-menu {
		flex: 0 1 70%;
		max-width: 70%;
		overflow-x: auto;
		display: flex;
		align-items: center;
		justify-content:start;
		a { 
			position: relative;
			display: block;
			color: #181818;
			font-size: 14px;
			font-weight: 600;
			font-family: Poppins;
			padding: 0 0 10px;
			margin-right: 30px;
			&.active {
				color: var(--theme-color);
				&::after {
					content: '';
					position: absolute;
					bottom: 0px;
					left: 50%;
					transform: translateX(-50%);
					width: 65%;
					height: 2px;
					background-color: var(--theme-color);
				}
			}
			&:last-child {
				margin-right: 0;
			}
		}
	}
	.header-social-bar {
		flex: 0 1 30%;
		max-width: 30%;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		li {
			margin-left: 8px;
			cursor: pointer;
		}
	}
	&.no-show-social {
		.page-header-menu {
			flex: 0 1 100%;
			max-width: 100%;
		}
	}
}

@media (max-width: 768px ) {
	.page-header {
		.page-header-menu {
			a {
				line-height: 42px;
				font-weight: 800;
				padding: 0;
				&.active {
					// color: var(--theme-color);
					color: #181818;
					font-size: 18px;
					&::after {
						opacity: 0;
					}
				}
				&:last-child {
					margin-right: 0;
				}
			}
		}
	}
}