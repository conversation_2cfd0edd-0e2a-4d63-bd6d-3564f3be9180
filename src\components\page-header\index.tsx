import { useState } from 'preact/hooks';
import cls from 'classnames';
import './style.less';
import { appendCreativeIdToHref } from '@utils';
import WTwPng from './social-image/mdi_twitter.png';
import WInsPng from './social-image/mdi__ins.png';
import WTtPng from './social-image/mdi_tiktok.png';
import WFbPng from './social-image/mdi_facebook.png';
interface IProps {
	activeCategory: string,
	seoCategoryListData: Array<string>;
	creativeId: string;
	showSocial?: boolean;
	mainDomain: string;
}

export default function PageHeader(props: IProps) {
	const { creativeId, seoCategoryListData, activeCategory, showSocial = true, mainDomain  } = props

	const handleShare = (site: string) => {
		let newsUrlList = encodeURIComponent(`https://${mainDomain}`);
		if (site === 'instagram') {
			// Instagram 通常需要使用应用内部的分享功能
			// 这里只能打开 Instagram 的创建页面
			window.open(`https://www.instagram.com/create/`);
		}
		if (site === 'twitter') {
			window.open(`https://twitter.com/intent/tweet?url=${newsUrlList}`);
		}
		if (site === 'facebook') {
			window.open(`https://www.facebook.com/sharer/sharer.php?u=${newsUrlList}`);
		}
		if (site === 'tiktok') {
			// TikTok 通常需要使用应用内部的分享功能
			// 这里只能打开 TikTok 的主页
			window.open('https://www.tiktok.com/');
		}
	}

	return (
		<div className={cls("page-header", showSocial ? 'show-social': 'no-show-social')}>
			<div className="page-header-menu">
				{
					seoCategoryListData && seoCategoryListData.map((item: string, index: number) => {
						let aHref = `/list/${item}`;
						// 如果是第一个分类 跳转到首页
						if (index === 0) {
							aHref = '/';
						}
						return (
							<>
								<a	key={item}
									href={appendCreativeIdToHref(aHref, creativeId)}
									className={cls('category-name', activeCategory === item && 'active')}
								>
									{item}
								</a>
							</>
						);
					})
				}
			</div>
			{
				showSocial ? (
					<ul className="header-social-bar">
						<li onClick={() => handleShare('twitter')}>
							<img src={WTwPng} alt=""/>
						</li>
						<li onClick={() => handleShare('instagram')}>
							<img src={WInsPng} alt=""/>
						</li>
						<li onClick={() => handleShare('tiktok')}>
							<img src={WTtPng} alt=""/>
						</li>
						<li onClick={() => handleShare('facebook')}>
							<img src={WFbPng} alt=""/>
						</li>
					</ul>

				) : null
			}
			
		</div>
	)
}
