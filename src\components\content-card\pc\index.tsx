import { useEffect, useState } from 'preact/hooks';
import cls from 'classnames';
import { getTimeSincePost, trackBiEvent, gtmEvent, appendCreativeIdToHref, getScaleImgSrc, getFormattedDate } from '@utils';
import './style.less';
import { IContent } from '@types';
import { LazyImg } from '@components';

interface IProps {
	content: IContent,
	category: string,
	imgDomain: string,
	creativeId: string,
	cardType: string,
	lang: string
}
export default function ContentCardPc(props: IProps) {
	const { content, category, imgDomain, creativeId, cardType,lang  } = props;

	let { id, title, published_time, type, path, abstract } = content;



	// type[1.站点内容, 2.search广告]
	const isSelfContent = type === 1;

	// const href = isSelfContent ? `/${id}` : link;
	const href = appendCreativeIdToHref(`/${lang}/${path || id}`, creativeId);

	const onContentClick = () => {
		if (!isSelfContent) {
			return
		}
		const params = {
			event_name: 'news_click',
			page_id: category
		}
		trackBiEvent('news_click', params);

		// 发送gtm事件: 内容点击事件
		gtmEvent('item_click', {
			event_label: title
		});
	}

	let size: string

	if (cardType === 'smallImageCard') {
		size = '360*214'
		return (
			<a className={cls('small-image-card')} href={href} onClick={onContentClick}>
				<div className="img">
					<LazyImg src={getScaleImgSrc(content, imgDomain, size)} alt={title} />
					{
						category ? <span className="image-card-category">{category}</span>: null
					}
				</div>
				<div className="content-card-pc-text-warp">
					<div className="title">
						{title}
					</div>
					<div className="time">
						{
							getFormattedDate(published_time)
						}
					</div>
				</div>
				
			</a>
		);
	} else if (cardType === 'bigImageCard'){
		size = '580*312'
		return (
			<a className={cls('big-image-card')} href={href} onClick={onContentClick}>
				<div className="big-image-card-warp">
					<LazyImg className="big-image-card-image"  src={getScaleImgSrc(content, imgDomain, size)} alt={title} />
					<div className="big-image-card-pc-text-warp">
						<div className="time">
							{
								getFormattedDate(published_time)
							}
						</div>
						<div className="title">
							{title}
						</div>
					</div>
				</div>
			</a>
		)
			
	} else if (cardType === 'textCard'){
		return (
			<a className={cls('text-card')} href={href} onClick={onContentClick}>
				<div className="text-card-warp">
					<div className="time">
						{
							getFormattedDate(published_time)
						}
					</div>
					<div className="title">
						{title}
					</div>
					<div className="content">
						{
							abstract ? abstract : `${title}${title}${title}${title}${title}`
						}
					</div>
				</div>
			</a>
		)
	} 
	
}
