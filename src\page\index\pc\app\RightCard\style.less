.right-content-card {
    display: flex;
    width: 292px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    flex-shrink: 0;
    // margin: 16px 0 16px 0;
}

.right-card-content-warp {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    align-self: stretch;
    width: 292px;

    .item-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        // gap: 16px;
        align-self: stretch;
    }

    .item-title {
        color: #080F18;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 25px;
        /* 156.25% */
        width: 263px;


    }

    .item-number {
        display: inline-block;
        color: #080F18;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 25px;
        width: 27px;
        /* 156.25% */

    }

    .item-time {
        color: #969696;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-top: 4px;
    }

}

.right-content-card-title {
    color: var(--theme-color);
    font-family: Inter;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.right-card-content-item {
    display: flex;
    cursor: pointer; // 可选，增强可点击感

    &:hover {

        .item-title,
        .item-number {
            color: var(--theme-color);
            font-style: italic;
            font-weight: 600;
            text-decoration-line: underline;
            text-decoration-style: solid;
            text-decoration-skip-ink: none;
            text-decoration-thickness: 10%;
            text-underline-offset: 25%;
            text-underline-position: from-font;
        }
    }

}

.right-svg {
    svg {
        path {
            stroke: var(--theme-color);
        }
    }
}