.index-wrap-pc {
	background-color: var(--theme-bg-color);

	.page-warp {
		min-height: 100vh;
	}

	.index-page-card-warp {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding-top: 16px;
		gap: 24px;
		width: 1200px;

		.swiper-wrap {
			width: calc(64% + 20px);
		}

		.small-image-card,
		.text-card {
			width: 32%;
		}

		>.big-image-card {
			width: calc(64% + 20px);
		}
	}

	.menu {
		display: flex;
		width: 1200px;
		padding: 8px 100px;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		gap: 10px;
		border-radius: 12px;
		background: var(--theme-color);
		box-shadow: 0px -2px 10px 0px rgba(0, 0, 0, 0.03), 0px 4px 10px 0px rgba(0, 0, 0, 0.08);

		.category-name {
			display: flex;
			padding: 10px;
			justify-content: center;
			align-items: center;
			gap: 10px;
			border-radius: 6px;
			color: #fff
		}

		a {
			color: #FFF;
			font-family: Inter;
			font-size: 14px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			text-transform: capitalize;

			&:hover {
				color: var(--theme-color);
				font-family: Inter;
				font-size: 14px;
				font-style: normal;
				font-weight: 600;
				line-height: normal;
				text-transform: capitalize;
				background: #fff;
			}
		}
	}

	.index-body-one {
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		gap: 40px;
		align-self: stretch;
		// background-color: #000;
		width: 1200px;
		height: 312px;

		.featured-news-card {
			width: 100%;
			height: 100%;
			position: relative;

			border-radius: 8px;

			.featured-image {
				width: 580px;
				height: 312px;
				border-radius: 8px;
			}

			.news-overlay {
				width: 580px;
				height: 125px;
				position: absolute;
				bottom: 0px;
				border-radius: 8px;
				background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.80) 100%);

				.news-des {
					margin: 18px 109px 30px 16px;
					color: #fff;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					gap: 8px;
					align-self: stretch;


				}

				.news-meta {
					color: #FFF;
					font-family: Inter;
					font-size: 14px;
					font-style: normal;
					font-weight: 700;
					line-height: 24px;
					/* 171.429% */
				}

				.news-time {
					color: #FFF;
					font-family: Inter;
					font-size: 14px;
					font-style: normal;
					font-weight: 400;
					line-height: 24px;
					/* 171.429% */
				}

				.news-title {
					color: #FFF;
					font-family: Inter;
					font-size: 18px;
					font-style: normal;
					font-weight: 700;
					line-height: normal;
				}
			}


		}

		.one-right {
			display: flex;
			width: 580px;
			height: 312px;
			flex-direction: column;
			align-items: flex-end;
			// gap: 24px;
			flex-shrink: 0;

			.svg-warp {
				width: 558px;
				height: 0px;
				flex-shrink: 0;
				margin-top: 24px;
				margin-bottom: 24px;
				margin-left: 22px;
			}

			.right-card {
				margin-left: 22px;
				display: flex;
				width: 558px;
				align-items: center;
				gap: 16px;
				height: 132px;
			}

			.right-card-title {
				display: flex;
				width: 364px;
				flex-direction: column;
				align-items: flex-start;
				gap: 8px;
				flex-shrink: 0;
				width: 364px;
				color: #080F18;
				font-family: Inter;
				font-size: 18px;
				font-style: normal;
				font-weight: 600;
				line-height: 25px;
				/* 138.889% */
			}

			.right-card-title-text {
				&:hover {
					width: 364px;
					color: var(--theme-color);
					font-family: Inter;
					font-size: 18px;
					font-style: italic;
					font-weight: 600;
					line-height: 25px;
					/* 138.889% */
					text-decoration-line: underline;
					text-decoration-style: solid;
					text-decoration-skip-ink: none;
					text-decoration-thickness: 10%;
					/* 1.8px */
					text-underline-offset: 25%;
					/* 4.5px */
					text-underline-position: from-font;
				}
			}

			.right-card-abs {
				width: 363px;
				color: #646464;
				font-family: Inter;
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 1.5rem;
				max-height: 3rem;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;

				/* 157.143% */
			}

			.right-card-title-time {
				color: #969696;
				font-family: Inter;
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: normal;
			}

			.right-card-img {
				border-radius: 8px;
				width: 170px;
				height: 100px;
				object-fit: cover;
			}


		}
	}

	.index-body-two {
		display: flex;
		width: 1200px;
		justify-content: flex-start;
		align-items: flex-start;
		gap: 16px;
		// margin-top:24px;

	}

	.index-body-th {
		width: 1200px;
		margin-top: 24px;
		margin-bottom: 24px;
	}

}