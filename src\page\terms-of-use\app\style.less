@import "../../../assets/css/config.less";
@import "../../../assets/css/common.less";

.terms-text-info {
	background-color: var(--theme-bg-color);
	.terms-text-content {
		max-width: 896px;
		margin: 0 auto;
		padding-top: var(--header_height);
		min-height: calc(100vh - 128px);

		a {
			color: var(#000);
		}

		.content-header {
			color: var(--gray-12, #000);
			font-size: 36px;
			font-weight: 800;
			text-transform: capitalize;
			padding: 45px 0 32px 0;
		}

		.content-container {
			font-size: 16px;
			line-height: 1.5;
		}
	}

	font-size: 16px;

	.mobile-inner {
		min-height: calc(100vh - 203px - 176px);

		.content-container {
			a {
				color: #000;
			}

			margin: 10px 12px 0;
		}
	}

	.content-mobile {
		margin: 0;

		.content-header {
			padding-left: 15px;
			font-size: 24px;
			font-weight: 700;
			padding-top: 5px;
		}
	}
}

body[data-system="mobile"] {
	.terms-of-use-page {
background: var(--theme-bg-color);
		// padding-top: 84px;
		.header-component-mobile {
			padding: 20px 12px 0;
		}

		.terms-text-content {
			padding: 0 12px 45px;
			margin: 0;
			font-family: Inter !important;
		}

		.terms-text-info {

			// padding: 16px;
			.content-header {
				// text-align: center;
			}
		}
	}
}