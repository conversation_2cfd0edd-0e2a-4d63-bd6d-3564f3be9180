import { IEnvMap, IPolicyAndContact } from "@types";
import "./style.less";
import HeaderPc from "@components/header-pc";
import { Footer, HeaderMobile } from "@components";
import { useEffect } from "preact/hooks";
import { adsComponentsInit } from "@utils";
import { getLocale } from "@utils/locales";
// import { HeaderSimpleMobile } from '@components';

interface IProps {
	seoEnvMap: IEnvMap;
	mainDomain: string;
	seoCategoryListData: Array<string>;
	policyAndContactRes: IPolicyAndContact;
}

export default function App(props: IProps) {
	const { seoEnvMap, seoCategoryListData, policyAndContactRes, mainDomain } =
		props;
	const {
		creativeId,
		theme: { logo },
		isMobile,
		lang,
		siteId,
		hostname,
		
	} = seoEnvMap;
	const { txt } = policyAndContactRes;
	const showTime =
		seoEnvMap.timeEnable && seoEnvMap.timeEnable.enable === false
			? false
			: true;
	useEffect(() => {
		adsComponentsInit();
	}, []);

	const renderContent = () => {
		return <div dangerouslySetInnerHTML={{ __html: txt }} />;
	};
	return (
		<section className="about-text-info">
			{isMobile ? (
				<>
					{/* <HeaderMobile
						seoEnvMap={seoEnvMap}
						seoCategoryListData={seoCategoryListData}
						seoActiveCategory={seoCategoryListData[0]}
						showTime={showTime}
						siteId={siteId}
					/>
					<div className="mobile-inner">
						<div className="content-mobile">
							<h3 className="content-header">
								{getLocale(lang, "about_us")}
							</h3>
						</div>
						<div className="content-container">
							{renderContent()}
						</div>
					</div> */}
				</>
			) : (
				<>
					<HeaderPc
					creativeId={creativeId} seoCategoryListData={seoCategoryListData} activeCategory={''} seoEnvMap={seoEnvMap} 
					/>
					<div className="about-text-content">
						<div>
							<h3 className="content-header">
								{getLocale(lang, "about_us")}
							</h3>
						</div>
						<div className="content-container">
							{renderContent()}
						</div>
					</div>
					<Footer
						creativeId={creativeId}
						lang={lang}
						logo={logo}
						hostname={hostname}

					/>
					{/* <Share seoEnvMap={seoEnvMap} /> */}
				</>
			)}
		</section>
	);
}
