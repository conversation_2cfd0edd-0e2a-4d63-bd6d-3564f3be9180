import { useState, useEffect } from 'preact/hooks';
import './style.less';

interface IProps {
	page: number
	totalRecords: number,
	onPageChanged: Function,
}

export default function Pagination(props: IProps) {
	const { totalRecords, onPageChanged, page } = props;
	const [currentPage, setCurrentPage] = useState(page || 1);
	const limitPerPage = 10;

	useEffect(() => {
		setCurrentPage(page || 1);
	}, [totalRecords]);

	const totalPages = Math.ceil(totalRecords / limitPerPage);

	const handlePageChanged = (page: number) => {
		setCurrentPage(page);
		onPageChanged(page);
	};

	const renderPageButtons = () => {
		const pageButtons = [];
		let startLoop = currentPage - 1;
		let endLoop = startLoop + 2;

		if (currentPage > 1) {
			pageButtons.push(
				<li key={0} className="page-item">
					<a className="page-link" onClick={() => handlePageChanged(currentPage - 1)} aria-label="Previous">
						<span aria-hidden="true">&laquo;</span>
					</a>
				</li>
			);
		}

		if (totalPages <= 3) {
			startLoop = 0;
			endLoop = totalPages;
		} else if (currentPage === totalPages) {
			startLoop = currentPage - 2;
			endLoop = currentPage;
		} else if (currentPage === totalPages - 1) {
			startLoop = currentPage - 1;
			endLoop = currentPage + 1;
		} else if (currentPage > 1 && currentPage < totalPages) {
			startLoop = currentPage - 1;
			endLoop = currentPage + 1;
		} else {
			startLoop = 0;
			endLoop = 2;
		}

		for (let i = startLoop; i < endLoop; i++) {
			pageButtons.push(
				<li key={i + 1} className={`page-item${currentPage === i + 1 ? ' active' : ''}`}>
					<a className="page-link" onClick={() => handlePageChanged(i + 1)}>
						{i + 1}
					</a>
				</li>
			);
		}

		if (currentPage !== totalPages && totalPages > 1) {
			pageButtons.push(
				<li key={totalPages + 1} className="page-item">
					<a className="page-link" onClick={() => handlePageChanged(currentPage + 1)} aria-label="Next">
						<span aria-hidden="true">&raquo;</span>
					</a>
				</li>
			);
		}

		return pageButtons;
	};

	if (!totalRecords) return null;

	return (
		<div>
			<nav>
				<ul className="pagination">
					{renderPageButtons()}
				</ul>
			</nav>
		</div>
	);
}
