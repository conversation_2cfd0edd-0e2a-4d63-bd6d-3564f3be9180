import { useEffect, useState } from 'preact/hooks';
import { IContent, IContentList, IEnvMap } from '@types';
import { <PERSON>Header, <PERSON>erP<PERSON>, Swiper, Footer } from '@components';
import { PATTERN, REG_SEARCH_TAG, REG_CONTENT_PAGINATION } from '@constants';
import { trackBiEvent, gtmEvent, getBrowser, adsComponentsInit, appendCreativeIdToHref, getTimeSincePost, getFormattedDate } from '@utils';
import './style.less';
import { getLocale } from '@utils/locales';
const adsMap: any = {};
let imgElements: any = [];
import LastNews from './LastNews';

interface IProps {
	seoDetailsData: IContent;
	seoCategoryListData: Array<string>;
	seoEnvMap: IEnvMap;
	seoCategoryDataList: any;
}

export default function App(props: IProps) {
	const { seoDetailsData, seoEnvMap, seoCategoryListData, seoCategoryDataList } = props;
	let { title, content, category_name = '', imgs, published_time, m_id } = seoDetailsData;
	const { zoneMap, theme, imgDomain, siteId, creativeId, mainDomain, lang, timeEnable, hostname } = seoEnvMap;
	const { logo } = theme || {};
	const [contentHtml, setContentHtml] = useState(content);
	const [time, setTime] = useState(null);
	const [loading, setLoading] = useState(false);
	const [imgArr, setImgArr] = useState([]);
	const isSpecial = siteId === '24786993';
	const [recommendList, setRecommendList] = useState([]);
	const showTime = timeEnable && timeEnable.enable === false ? false : true;
	// 发送事件
	const sendEvent = (event_name: string, pageCategory?: string) => {
		const params: any = {
			event_name,
			page_id: pageCategory || category_name
		}
		if (event_name === 'news_page_leave') {
			params.page_stayduration = (new Date().getTime() - time) / 1000
		}
		trackBiEvent(event_name, params);
	}

	useEffect(() => {
		// 发送gtm事件: 内容页进入事件
		gtmEvent('content_pv', {
			event_label: title
		});
		if (imgs && imgs.length) {
			const useWebp = getBrowser() !== 'safari';
			const suffix = useWebp ? '.webp' : '.jpeg';
			const _imgs = imgs.map(item => `${imgDomain}/image/1143*600/${item}${suffix}?type=news_icon`);
			setImgArr(_imgs);
		}
		stringHtmlInsetAdDom(content);
		adsComponentsInit().then(() => {
			window.adsTag.cmd.push(() => {
				const { adsTag } = window;
				const { detail_pc_right_300x250 } = zoneMap;
				const adsConfig = [
					{ containerSelector: '#detail-pc-right-ad-id', width: 300, height: 250, zone: detail_pc_right_300x250 }
				];
				adsConfig.forEach(({ containerSelector, width, height, zone }) => {
					const container = document.querySelector(containerSelector);
					if (container) {
						adsTag.renderAds(container, width, height, zone);
					}
				});
			});
		});
		setLoading(true);
		setTime(new Date().getTime());
	}, []);

	useEffect(() => {
		if (!loading) {
			return
		}
		sendEvent('news_page');
		let isSendLeave = false;

		window.addEventListener('pagehide', function () {
			if (!isSendLeave) {
				sendEvent('news_page_leave');
				isSendLeave = true;
			}
		}, false);
		window.addEventListener('pagehide', function () {
			if (!isSendLeave) {
				sendEvent('news_page_leave');
				isSendLeave = true;
			}
		}, false);
	}, [loading])
	useEffect(() => {
		let list = seoCategoryDataList.listData.filter(item => item.m_id !== m_id);
		setRecommendList(list);
	}, [seoCategoryDataList, category_name])

	const replacer = (reg: string, p1: string) => {
		return `<iframe width="100%" height="315px" src="https://www.youtube.com/embed/${p1}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe/>`;
	}

	const createAdDom = (id: string) => {
		const dom = document.createElement('div');
		dom.setAttribute('id', id);
		dom.setAttribute('class', 'ad-wrap');
		return dom
	}

	// 给返回的内容里面插入渲染ad的dom
	const stringHtmlInsetAdDom = (stringHtml: string) => {
		const parser = new DOMParser();
		const doc = parser.parseFromString(stringHtml, 'text/html');
		let pElements: any = [];
		if (isSpecial) {
			imgElements = doc.querySelectorAll('img');
			imgElements.forEach((img: HTMLElement, index: number) => {
				img.setAttribute('loading', 'lazy');
				const adDom = createAdDom(`content-ad-${index}`);
				img.insertAdjacentElement('afterend', adDom);
			})
		} else {
			// 包装 h3 标签下的文本内容
			const h3Elements = doc.querySelectorAll('h3');
			h3Elements.forEach(h3 => {
				let nextNode = h3.nextSibling;
				while (nextNode && (nextNode.nodeType !== Node.TEXT_NODE || !nextNode.textContent.trim())) {
					nextNode = nextNode.nextSibling;
				}
				if (nextNode && nextNode.nodeType === Node.TEXT_NODE) {
					const p = doc.createElement('p');
					p.textContent = nextNode.textContent;
					nextNode.parentNode.replaceChild(p, nextNode);
				}
			});
			pElements = doc.querySelectorAll('p');
			const bannerAdDom1 = pElements[0];
			if (bannerAdDom1) {
				const bannerDom = createAdDom('content-ad-1');
				bannerAdDom1.appendChild(bannerDom);
			}
			const bannerAdDom2 = pElements[3];
			if (bannerAdDom2) {
				const bannerDom = createAdDom('content-ad-2');
				bannerAdDom2.appendChild(bannerDom);
			}
			const bannerAdDom3 = pElements[6];
			if (bannerAdDom3) {
				const bannerDom = createAdDom('content-ad-3');
				bannerAdDom3.appendChild(bannerDom);
			}
		}
		// 内容清除 Relatedsearches 标识
		let relatedSearchesElements = Array.from(pElements).filter(p => p.textContent.trim() === "Relatedsearches");
		relatedSearchesElements.forEach(p => {
			p.parentNode.removeChild(p);
		})

		let innerHtml = doc.documentElement.querySelector('body').innerHTML;
		innerHtml = innerHtml.replace(PATTERN, replacer);
		innerHtml = innerHtml.replace(REG_SEARCH_TAG, '');
		innerHtml = innerHtml.replace(REG_CONTENT_PAGINATION, '');
		setContentHtml(innerHtml);
	}

	// 事件: 分类切换
	const onCategoryClick = (name: string) => {
		sendEvent('page_reach', name);
		window.location.href = appendCreativeIdToHref(`/list/${name}`, creativeId);
	}

	const renderImg = () => {
		console.log(imgArr);

		if (imgArr.length) {

			<div className="carousel">
				<img src={imgArr[0]} alt="" />
			</div>



			// if (imgArr.length > 1) {
			// 	return (
			// 		<div className="carousel">
			// 			<Swiper swiperName="swiper_1" swiperList={imgArr} swiperHeight={400} swipeWidth={1114} swiperGap={0} />
			// 		</div>
			// 	)
			// }
		} else {
			return null
		}
	}

	if (!loading) {
		return null
	}
	function formatDateTimeLocal(timeString: string) {
		const date = new Date(timeString)
		const year = date.getFullYear()
		const month = String(date.getMonth() + 1).padStart(2, "0")
		const day = String(date.getDate()).padStart(2, "0")
		const hours = String(date.getHours()).padStart(2, "0")
		const mins = String(date.getMinutes()).padStart(2, "0")
		return `${year}-${month}-${day} ${hours}:${mins}`
	}

	return (
		<section className="content-wrap-pc">



			<HeaderPc logo={logo} creativeId={creativeId} seoCategoryListData={seoCategoryListData} seoEnvMap={seoEnvMap} />

			<div className="content-page-warp">
				<div className="header-title">
					<div className="title-box">
						<a
							href={appendCreativeIdToHref(`/${lang}/`, creativeId)}
							className="home"
						>
							{getLocale(lang, "home")}
						</a>
						/
						<span className="category">
							{seoDetailsData.category_name}
						</span>
					</div>
				</div>
				<div className="content-body">
					{seoDetailsData.content ? (

						<>
							<div className="content-title">{title}</div>
							{showTime && (
								<div className="content-time">{formatDateTimeLocal(published_time)}</div>
							)}
							<div >
								<img className="img" src={imgArr[0]} alt="" />
							</div>
							<div
								className="content-txt"
								dangerouslySetInnerHTML={{ __html: contentHtml }}></div>
						</>

					) : (
						<div className="not-found">
							{getLocale(lang, "not found")}
						</div>
					)

					}
					<LastNews title={getLocale(lang, 'recommend')} seoEnvMap={seoEnvMap} seoNewsList={recommendList.slice(0, 4)} />
				</div>
			</div>
			<Footer creativeId={creativeId} lang={lang} logo={logo} hostname={hostname} />
			{/* <div className="page-warp">
				<PageHeader activeCategory={seoCategoryListData[0]} seoCategoryListData={seoCategoryListData} creativeId={creativeId} mainDomain={mainDomain} />
				<div className="content-warp">
					<div className="content-left-warp">
						<h1 className="title">
							{title}
						</h1>
						<div className="time">
							{getFormattedDate(published_time)}
						</div>
						<div className="line" /> */}
			{
				// 				renderImg()
				// 			}
				// 			<div className="content-text" dangerouslySetInnerHTML={{ __html: contentHtml }} />
				// 		</div>
				// 		<div className="content-right-warp">
				// 			<div id="detail-pc-right-ad-id" className="detail-pc-right-ad"/>
				// 		</div>

				// 	</div>
				// </div>

				// <Footer creativeId={creativeId} /> 
			}
		</section>
	)
}
