import { renderToString } from 'preact-render-to-string';
import App from './app';
import { getSsrHtml } from '@utils/ssr';
import { IContent, IEnvMap, IF2eFiles } from '@types';

interface IProps {
	seoDetailsData: IContent;
	seoEnvMap: IEnvMap;
	seoCategoryListData: Array<string>;
}
export default function SSR(env: string, props: IProps, f2eFiles: IF2eFiles) {
	const content = renderToString(
		<App {...props}/>
	);

	return getSsrHtml(env, 'content-pc', content, props, f2eFiles);
}
