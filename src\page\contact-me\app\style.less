@import "../../../assets/css/config.less";

body {
	margin: 0;
	background-color: #fff;
	text-transform: capitalize;

	.contact-me-page {
		font-size: 16px;
		margin: 0 auto;
		background-color: var(--theme-bg-color);
		.contact-me-text-info {
			min-height: calc(100vh - 138px);
			padding-top: var(--header_height);
			width: 896px;
			margin: 0 auto;

			.contact-title {
				color: var(--font-color);
				font-size: 36px;
				font-weight: 600;
				margin: 0 auto;
				padding: 45px 0 15px;
				text-transform: capitalize;
			}

			a {
				display: inline-block;
				color: var(--theme-color);
			}
		}
	}
}

body[data-system="mobile"] {
	.contact-me-page {
		// padding-top: 84px;
		background: var(--theme-bg-color);

		.mobile-contact {
			.header-component-mobile {
				padding: 20px 12px 0;
			}

			.contact-me-text-info {
				padding: 0 12px 45px;
				margin: 0;
				font-family: Inter !important;
				width: 100%;
			}

			h2 {
				font-family: Inter !important;
			}
		}

		.mobile-inner {
			.content-mobile {
				margin-top: 16px;

				.content-header {
					text-align: center;
				}
			}

			.content-container {
				div {
					text-align: center;
					// color: #fb5092;
					color: #000;
				}
			}
		}
	}
}