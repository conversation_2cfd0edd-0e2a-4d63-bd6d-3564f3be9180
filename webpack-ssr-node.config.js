const path = require('path');
const webpack = require('webpack');
const argv = require('yargs').argv;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const { version, name } = require('./package.json');
const resolve = dir => path.resolve(__dirname, dir);
const serverPath = path.join(__dirname, 'server', 'page');
const entryOrigin = argv.env && argv.env.entryorigin || '';

console.log(`build ssr node, entryOrigin=[${entryOrigin}]`);
module.exports = {
	name: "ssr node",
	mode: 'production',
	entry: {
		'index-mobile': './src/page/index/mobile/ssr.tsx',
		'index-pc': './src/page/index/pc/ssr.tsx',
		'list-mobile': './src/page/list/mobile/ssr.tsx',
		'list-pc': './src/page/list/pc/ssr.tsx',
		'content-mobile': './src/page/content/mobile/ssr.tsx',
		'content-pc': './src/page/content/pc/ssr.tsx',
		'search-result-mobile': './src/page/search-result/mobile/ssr.tsx',
		'search-result-pc': './src/page/search-result/pc/ssr.tsx',
		error: './src/page/error/ssr.tsx',
		'privacy-policy': './src/page/privacy-policy/ssr.tsx',
		'contact-me': './src/page/contact-me/ssr.tsx',
		'terms-of-use': './src/page/terms-of-use/ssr.tsx',
		'about-us': './src/page/about-us/ssr.tsx'
	},
	// target: 'node',
	output: {
		path: serverPath,
		publicPath: `/${name}/`,
		// filename: "page.generator.js",
		filename: `${version}/[name].ssr.js`,
		// target: 'node',
		// 使用page.generator.js的是nodejs，所以需要将
		// webpack模块转化为CMD模块
		library: 'page',
		libraryTarget: 'commonjs'
	},
	resolve: {
		alias: {
			'@components': resolve('src/components'),
			'@common': resolve('src/common'),
			'@constants': resolve('src/constants'),
			'@http': resolve('src/common/http'),
			'@assets': resolve('src/assets'),
			'@utils': resolve('src/utils'),
			'@stores': resolve('./src/stores')
		},
		extensions: ['.js', '.jsx', '.ts', '.tsx']
	},
	module: {
		rules: [
			{ test: /\.js/, use: ['babel-loader?cacheDirectory=true'] },

			{
				test: /\.jsx?$/,
				// thread-loader：放置在这个 loader 之后的 loader 就会在一个单独的 worker 池中运行
				use: ['babel-loader?cacheDirectory=true'],
				// 不使用cache-loader的时候，可以在babel-loader的options中设置cacheDirectory: true
				include: [path.resolve(__dirname, 'src')],
				exclude: /node_modules/
			},
			{
				test: /\.tsx?$/,
				include: [path.resolve(__dirname, 'src')],
				use: ['babel-loader?cacheDirectory=true'],
				exclude: /node_modules/
			},
			{
				test: /\.(le|c)ss$/,
				use: [
					{
						loader: MiniCssExtractPlugin.loader,
						options: { esModule: true }
					},
					{
						loader: 'css-loader',
						options: {
							modules: false,
							importLoaders: 1,
							url: true, // 启用/禁用 url() 处理
							sourceMap: false // 启用/禁用 Sourcemaps
						}
					},
					{
						loader: 'postcss-loader',
						options: {
							postcssOptions: {
								// 使用插件
								plugins: [
									'postcss-import', // 支持@import 引入css
									'autoprefixer', // CSS浏览器兼容
									'cssnano' // 压缩css
								]
							}
						}
					},
					{
						loader: 'less-loader',
						options: {
							sourceMap: true // 启用/禁用 Sourcemaps
						}
					}
				],
				include: path.resolve(__dirname, './src')
			},
			{
				// 图片、字体等处理
				test: /\.(png|jpg|jpeg|gif|webp|svg|eot|ttf|woff|woff2|csv|docx)$/,
				use: [
					{
						loader: 'url-loader',
						options: {
							limit: 1024,
							esModule: false, // 文件加载器生成使用ES模块语法的JS模块
							name: '[name].[ext]', // 打包出的文件名称
							outputPath: 'assets' // 文件过大时输出到名称为assets的文件夹中
						}
					}
				]
			},

			{
				test: /\.tpl\.html$/,
				use: [
					{
						loader: 'html-loader'
					}
				],
				include: [path.resolve(__dirname, 'src')],
				exclude: /node_modules/
			}
		]
	},
	plugins: [
		new CleanWebpackPlugin(),
		new webpack.DefinePlugin({
			'process.env': {
				ENTRY_ORIGIN: JSON.stringify(entryOrigin),
				API_DOMAIN: JSON.stringify(''),
				NODE_ENV: JSON.stringify('production'),
				VERSION: JSON.stringify(version),
				BUILD_NAME: JSON.stringify(name)
			}
		}),
		new webpack.ProvidePlugin({
			h: ['preact', 'h'],
			Fragment: ['preact', 'Fragment']
		}),
		// 抽离样式文件到单独目录
		new MiniCssExtractPlugin({
			filename: `${version}/[name].ssr.css`,
			chunkFilename: '[id].css'
			// ignoreOrder: false, // Enable to remove warnings about conflicting order
		}),
		new OptimizeCSSAssetsPlugin({})
	]
}
