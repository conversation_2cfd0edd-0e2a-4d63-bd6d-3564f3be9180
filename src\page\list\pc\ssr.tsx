import { renderToString } from 'preact-render-to-string';
import App from './app';
import { getSsrHtml } from '@utils/ssr';
import { IEnvMap, IF2eFiles } from "@types";

interface IProps {
	seoCategory: string;
	seoEnvMap: IEnvMap;
	seoCategoryListData: Array<string>
}
export default function SSR(env: string, props: IProps, f2eFiles: IF2eFiles) {
	const content = renderToString(
		<App {...props}/>
	);

	return getSsrHtml(env, 'list-pc', content, props, f2eFiles);
}
