.pagination {
	display: flex;
	justify-content: center;
	margin: 10px 0;
}

.page-link {
	display: block;
	padding: 0.5rem 0.75rem;
	color: #007bff;
	background-color: transparent;
	border: 1px solid #dee2e6;
	border-radius: 0;
}

.page-link:hover {
	background-color: #f5f5f5;
	border-color: #dee2e6;
}

.page-link:focus {
	outline: 0;
	box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.page-item {
	margin-right: 0.25rem;
	margin-left: 0.25rem;
}

.page-item:first-child .page-link,
.page-item:last-child .page-link {
	border-radius: 0.25rem;
}

.page-item.disabled .page-link {
	color: #6b6b6b;
	background-color: transparent;
	border-color: #ddd;
	cursor: default;
	opacity: 0.5;
}

.page-item.active .page-link {
	color: #fff;
	background-color: #007bff;
	border-color: #007bff;
}
