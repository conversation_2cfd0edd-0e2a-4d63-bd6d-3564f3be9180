
import { IContent, IEnvMap, ISystem } from 'typings/types';

/**
 * 获取相应的环境变量，由node端传递
 */
export function getEnvVar(): IEnvMap {
	return window.APP_PROPS.seoEnvMap;
}


/**
 * 获取当前系统信息
 */
export const getSystem = (): ISystem => {
	return window.adsTag.system || {};
}

export const adsComponentsInit = () => {
	const { zoneMap, siteId } = getEnvVar();
	return new Promise((resolve) => {
		window.adsTag.cmd.push(() => {
			// 放在这里这个system一定是有值的
			const system = getSystem();
			const isMobile = system.isPhone || system.isAndroid;
			// 渲染穿插广告
			// 穿插
			if (zoneMap.webinsterstial) {
				window.adsTag.renderInterstitial(zoneMap.webinsterstial);
			}

			// 底部锚定广告(不是所有的域名都开启，需要根据zoneMap来决定是否加载)
			if (zoneMap.anchor) {
				window.adsTag.renderAnchor(zoneMap.anchor);
			}

			// 宽屏插入对联广告
			if (system.isPC && zoneMap.sidewall) {
				window.adsTag.renderSidewall(zoneMap.sidewall);
			}

			resolve(null);
		});
	});
}



/**
 * 在客户端获取服务端渲染时所需要的数据
 */
export const getSsrProps = () => {
	return (window as any).APP_PROPS || {};
}

/**
 * 获取发布距今的时间，可能返回的格式: ['xx天之前', 'xx小时之前', 'xx分钟之前', 'xx秒之前']
 * @param value: 距今的秒
 */
export const getTimeSincePost = (value: number) => {
	// 相差天数
	const day = Math.floor(value / (24 * 3600));

	// 计算天数后剩余的秒
	const leave1 = value % (24 * 3600);

	//计算出小时数
	const hours = Math.floor(leave1 / 3600);

	// 计算小时数后剩余的秒
	const leave2 = leave1 % 3600;

	// 计算相差分钟数
	const minutes = Math.floor(leave2 / 60);

	// 计算分钟数后剩余的秒
	const seconds = leave2 % 60;

	let timesString = '';
	if (day !== 0) {
		timesString = `${day.toString()} day ago`;
	} else if (day === 0 && hours !== 0) {
		timesString = `${hours.toString()} hour ago`;
	} else if (day === 0 && hours === 0 && minutes !== 0) {
		timesString = `${minutes.toString()} min ago`;
	} else if (day === 0 && hours === 0 && minutes === 0) {
		timesString = `${seconds.toString()} sec ago`;
	}

	return timesString;
}

/**
 * 获取发布时间 格式为 Mar 26, 2024 at 11:20 AM
 * @param value: 距今的秒
 */
export const getFormattedDate = (isoString: string) => {
	const date = new Date(isoString); // Convert seconds to milliseconds
	const options: Intl.DateTimeFormatOptions = {
	  year: 'numeric',
	  month: 'short',  // e.g. "Mar"
	  day: '2-digit',
	  hour: 'numeric',
	  minute: '2-digit',
	  hour12: true     // 12-hour format with AM/PM
	};
  
	const formattedDate = date.toLocaleString('en-US', options);
	return formattedDate;
  };

// 获取UID
export const getUid = () => {
	return window.adsTag?.uid;
}

// 等待获取uid, 未存在时会一直等待到存在 再返回
export const waitGetUid = (): Promise<string> => {
	return new Promise((resolve) => {
		let uid = getUid();
		if (uid) {
			return resolve(uid);
		}
		const siv = setInterval(() => {
			uid = getUid();
			if (uid) {
				clearInterval(siv);
				resolve(uid);
			}
		}, 50);
	});
}

export const formatUrlParamsData = (queryString: string): any => {
	const str = queryString.replace(/^\?/, '');
	const arr = str.split('&');

	return arr.reduce((result, current) => {
		let [key, value = ''] = current.split('=');

		return { ...result, [key]: value };
	}, {});
};

export const sendEventMethod = (event_name: string, params: any) => {
	const query = new URLSearchParams(params);
	const url = `https://apicpm.yeahtargeter.com/searchBI?${query}`;
	fetch(url, {
		method: 'GET',
		mode: 'cors', // 设置为CORS模式
		keepalive: true
	});
	gtmEvent(event_name, {});
}

/**
 * 提交BI事件(Navigator.sendBeacon服务)
 * @param eventName
 * @param customData: 自定义的字段
 */
export const trackBiEvent = (eventName: string, customData?: any) => {
	window.adsTag.cmd.push(() => {
		window.adsTag.sendBI(eventName, customData);
	});
}

// 发送gtm事件
export const gtmEvent = (eventName: string, conf: any) => {
	window.adsTag.cmd.push(() => {
		window.adsTag.sendGTM(eventName, conf);
	});
}

/**
 * 获取cookie中的值
 * @param key
 */
export const getCookieItem = (key: string) => {
	try {
		const list = document.cookie.split('; ');
		const cookieMap = {};
		list.forEach(item => {
			const itemSplit = item.split('=');
			cookieMap[itemSplit[0]] = itemSplit[1];
		});
		return cookieMap[key] || '';
	} catch (e) {
		console.error('cookie error', e);
		return '';
	}
};
//
/**
 * 验证是否为空对像
 * @param v
 */
export function isEmpty(v: string | object): boolean {
    return (
        v === undefined ||
        v === null ||
        (typeof v === 'string' && v.trim() === '') ||
        (Object.prototype.toString.apply(v) === '[object Object]' && JSON.stringify(v) === '{}') ||
        (Array.isArray(v) && v.every(item => isEmpty(item)))
    );
}

/**
 * 获取当前浏览器信息
 */
let browser: string;
export const getBrowser = (): string => {
	if (browser) {
		return browser;
	}
	try {
		const list = navigator.userAgent.toLowerCase().match(/(msie|firefox|chrome|opera|version|safari).*?([\d.]+)/);

		let str = list[1].replace(/version/, 'safari');
		if (navigator.userAgent.toLowerCase().indexOf('edg') !== -1) {
			str = 'edge';
		}
		if (list['input'].indexOf('AppBrowser') !== -1) {

		}
		browser = str;
		return browser;
	} catch (e) {
		browser = '';
		return browser;
	}
};


/**
 * 获取当前浏览器信息
 */
let AppBrowser: string;
export const getAppBrowser = (): string => {
	if (AppBrowser) {
		return AppBrowser;
	}
	try {
		const list = navigator.userAgent.toLowerCase().match(/(msie|firefox|chrome|opera|version|safari).*?([\d.]+)/);

		let str = '';
		if (list['input'].indexOf('appbrowser') !== -1) {
			str = 'appbrowser'
		}
		AppBrowser = str;
		return AppBrowser;
	} catch (e) {
		AppBrowser = '';
		return AppBrowser;
	}
};




// 获取原图的src
export const getImgSrc = (content: any, imgDomain: string) => {
	const useWebp = getBrowser() !== 'safari';
	const suffix = useWebp ? '.webp' : '.jpeg';

	// 如果没有 icon_id，则使用原图，否则使用带后缀的图
	let imgUrl: string;
	if (content.icon_id) {
		imgUrl = `${imgDomain}/news_icon/${content.imgs && content.imgs.length > 0 ? content.imgs[0] : content.icon_id}${suffix}`;
	} else {
		imgUrl = `${content.icon.indexOf('http') === 0 ? content.icon : `${imgDomain}/news_icon/${content.icon}`}`;
	}


	return imgUrl;
}


/**
 * 获取图片icon地址
 * @param content // icon的obj
 * @param imgDomain // 图片地址
 * @param size /裁剪大小
 */
export const getScaleImgSrc = (content: IContent, imgDomain: string, size:string): string => {
	// 根据浏览器判断是否使用 WebP 格式
	const isSafari = getBrowser() === 'safari';
	const suffix = isSafari ? '.jpeg' : '.webp';
	// 获取图片路径的优先级：图片icon_id -> icon -> img[0]
	// icon_id不一定有
	if (content.icon_id) {
		return `${imgDomain}/image/${size}/${content.icon_id}${suffix}?type=news_icon`;
	}
	// icon有时候是https开头的  有时候是cqq8qpta5eb5lt21rjpg.jpg
	if (content.icon) {
		// 如果包含. 且不包含http 说明有后缀
		if (content.icon.includes('.') && !content.icon.includes('http')) {
			return `${imgDomain}/image/${size}/${content.icon}?type=news_icon`;
		}
		// 包含http的情况
		return content.icon;
	}
	// imgs字段 有时候是一个数组，有时候为undefined
	if (content.imgs?.[0]) {
		return `${imgDomain}/image/${size}/${content.imgs?.[0]}${suffix}?type=news_icon`;
	}
}

// 给href添加creative_id
export const appendCreativeIdToHref = (href: string, creativeId: string) => {
	if(!creativeId){
		return href;
	}
	return `${href}?w_cid=${creativeId}`;
}


/**
* 获取发布时间 格式为 2025-02-17 11:20
* @param value: 距今的秒
*/
export const getFormattedNumberDate = (isoString: string) => {
	const date = new Date(isoString); // Convert seconds to milliseconds

	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hour = String(date.getHours()).padStart(2, '0');
	const minute = String(date.getMinutes()).padStart(2, '0');

	// Format the date as YYYY-MM-DD HH:MM
	const formattedDate = `${year}-${month}-${day} ${hour}:${minute}`;
	return formattedDate;
};


export const timeAgo = (dateString: string)=> {
  const now = new Date();
  const target = new Date(dateString);
  const diffMs = now - target;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays < 1) {
    return 'Today';
  } else if (diffDays === 1) {
    return '1 day ago';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    const weeks = Math.floor(diffDays / 7);
    return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
  }
}