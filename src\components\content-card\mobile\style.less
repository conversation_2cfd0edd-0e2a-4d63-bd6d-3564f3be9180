.small-image-mobile-card{
	border: 1px solid var(--border_color);
	display: block;
	border-radius: var(--theme-border-radius);
	background-color: var(--theme-pc-content-card-bg);
	position: relative;
	.image-card-category {
		top: 0;
		border-top-left-radius: var(--theme-border-radius);
	}
	.img {
		position: relative;
		width: 100%;
		padding-top: 62.4%;
		overflow: hidden;
		img {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			right: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			border-top-right-radius: var(--theme-border-radius);
			border-top-left-radius: var(--theme-border-radius);
		}
	}
	.content-card-pc-text-warp {
		padding: 18px; 
		.title {
			min-height: 48px;
			font-size: 16px;
			color: #000;
			font-weight: 600;
			margin-bottom: 12px;
		}
		
	}
	.content-card-mobile-text-warp {
		padding: 10px;
		.time {
			font-size: 10px;
			color: #9F9F9F;
		}
		.title {
			font-size: 11px;
			font-family: Poppins;
			font-weight: 600;
			color: #000;
			margin-bottom: 5px ;
			// font-weight: 500;
			display: -webkit-box;         /* 必须结合的用于支持多行截断 */
			-webkit-line-clamp: 2;        /* 限制显示的行数 */
			-webkit-box-orient: vertical; /* 设置盒子布局为垂直方向 */
			overflow: hidden;             /* 隐藏多余的文本 */
			text-overflow: ellipsis;      /* 使用省略号表示截断的文本 */
		}
	}
}

.big-image-mobile-card{
	display: block;
	// border-radius: var(--theme-border-radius);
	border-radius: 6px;
	.big-image-card-warp {
		width: 100%;
		padding-top: 45.02%;
		position: relative;
		overflow: hidden;
		margin-bottom: 15px;
		.image-card-category {
			top: 0;
			border-top-left-radius: var(--theme-border-radius);
		}
		img {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			right: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			// border-radius: var(--theme-border-radius);
			border-radius: 6px;
		}
	}
	.big-image-card-mobile-text-warp {
		position: relative;
		width: 100%;
		.title {
			font-size: 16px;
			color: #000;
			margin-bottom: 8px;
			font-family: Arial Black;
			display: -webkit-box;         /* 必须结合的用于支持多行截断 */
			-webkit-line-clamp: 2;        /* 限制显示的行数 */
			-webkit-box-orient: vertical; /* 设置盒子布局为垂直方向 */
			overflow: hidden;             /* 隐藏多余的文本 */
			text-overflow: ellipsis;      /* 使用省略号表示截断的文本 */
		}
		.time {
			font-size: 12px;
			font-family: Aref Ruqaa;
			color: #9F9F9F;
			margin-bottom: 12px;
		}
	}
	
	
}