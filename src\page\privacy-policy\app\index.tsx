import { useEffect } from 'preact/hooks';
import { adsComponentsInit, appendCreativeIdToHref } from '@utils';
import { FooterMobile, Footer, HeaderPc, HeaderMobile } from '@components';
import { IEnvMap, IPolicyAndContact } from '@types';
import { getLocale } from '@utils/locales';
import './style.less';
interface IProps {
	seoPolicyAndContact: IPolicyAndContact
	seoEnvMap: IEnvMap;
	seoCategoryListData: Array<any>;
}

export default function App(props: IProps) {
	const { seoPolicyAndContact, seoEnvMap, seoCategoryListData } = props;
	const { creativeId, theme, isMobile, lang, mainDomain } = seoEnvMap;
	const { logo } = theme || {};
	const { txt } = seoPolicyAndContact;
	useEffect(() => {
		adsComponentsInit();
	}, []);

	const renderContent = () => {
		return (
			<div dangerouslySetInnerHTML={{ __html: txt }} />
		)
	}
	return (
		<>
			{isMobile ? (
				<>
					<HeaderMobile seoEnvMap={seoEnvMap} seoCategoryListData={seoCategoryListData} activeCategory={""} isShowSearch={false} />
					<section className="text-info">
						<div className="text-content">
							<h2 className="content-header">{getLocale(lang, 'privacy_policy')}</h2>
							{renderContent()}
						</div>
					</section>
					
				</>
			) : (
				<div className="privacy-policy-content">
					<HeaderPc
						activeCategory={''}
						logo={logo}
						seoCategoryListData={seoCategoryListData}
						seoEnvMap={seoEnvMap}

						creativeId={creativeId}
					/>
					<section className="text-info">
						<div className="text-content">
							<h2 className="content-header">{getLocale(lang, 'privacy_policy')}</h2>
							<div className="content-inner">
								<div className="contact-img"><a href="/" className="back" /></div>
							</div>
							{renderContent()}
						</div>
					</section>
					<Footer
						creativeId={creativeId} lang={lang} logo={logo} isMobile={isMobile} hostname={mainDomain}
					/>
				</div>
			)}
			{
				isMobile ? <Footer creativeId={seoEnvMap.creativeId} lang={seoEnvMap.lang} logo={seoEnvMap.theme.logo} hostname={seoEnvMap.hostname} isMobile={true} />
					: null
			}
		</>

	);
}
