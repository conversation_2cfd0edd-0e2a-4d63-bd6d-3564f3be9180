#!/bin/bash
shopt -s extglob
COMPONENT=$1
curdir=$(cd "$(dirname "$0")"; pwd)

function Log(){
    dateFormat=`date "+%Y-%m-%d %H:%M:%S"`
    level="INFO"
    messageShow="message is null"
    if [[ $1 != "" ]];then
        typeset -u level=$1
    fi
    if [[ $2 != "" ]];then
        messageShow=$2
    fi
    printf "[%s] %s %s\n" "$level" "$dateFormat" "$messageShow"
}
function isError(){
  if [ $? -ne 0 ];then
      Log ERROR "build failure"
    exit 1
  fi
}
function build(){
    Log INFO "[CMD] npm install"
    npm install
    isError
    Log INFO "[CMD] npm run build-web"
    npm run build:web
    isError
    Log INFO "[CMD] npm run build-node"
    npm run build:ssr-node
    isError
}
function cleanSpace(){
    rm -rf !(content-site|server|deploy|node_modules|package.json|api-domain.json|host-site.json)
}

build
cleanSpace


