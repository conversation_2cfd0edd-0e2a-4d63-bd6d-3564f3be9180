@import '../../assets/css/config';
.swiper-wrap {
	overflow: hidden;
	position: relative;
	width: 100%;
	// height: 100%;
	.container {
		display: flex;
		transition: transform 0.5s ease-in-out;
		width: 100%;
		position: absolute;
		white-space: nowrap;
		left: 0;
		top: 0;
		.items {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			width: 100%;
		}
	}
	.count-wrap {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		bottom: 10px;
		display: flex;
		height: 50px;
		align-items: center;
		justify-content: center;
		gap: 16px;
		.arrow {
			width: 25px !important;
			height: 25px !important;
		}
		.count {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 6px;
			div{
				width: 10px;
				height: 8px;
				border-radius: 3px;
				background: rgba(255, 255, 255, 0.50);
				transition: all 0.5s ease-in-out;
				cursor: pointer;
			}
			.active {
				width: 26px;
				height: 8px;
				background: #FFF;
			}
		}
	}
}
