import axios from 'axios';
import { API_PREFIX } from '@constants';
import { getEnvVar } from '@utils';
const http = axios.create();
http.interceptors.request.use(config => {
	return config;
});
http.interceptors.response.use(
	resp => {
		return resp.data;
	},
	error => {
		// 请求被取消了，不以error方式处理
		if (axios.isCancel(error)) {
			// console.log('request cancel');
			return;
		}
		let msg = '';
		if (error.response) {
			msg = error.response?.data?.message || error.response?.data?.msg || '服务器响应错误';
		} else if (error.request) {
			msg = error.request || '请求发生错误';
		} else {
			msg = error.message || '本地错误';
		}
		throw Error(msg);
	}
);

// 标准服务
export const basicService = new Proxy(http, {
	get(target, prop) {
		const { apiDomain } = getEnvVar();
		// 构建工具不支持在build的阶段生成对应的包，改为了在代码中分捡的方式
		// http.defaults.baseURL = `${API_DOMAIN}${API_PREFIX}`;
		http.defaults.baseURL = `${apiDomain}${API_PREFIX}`;
		return target[prop];
	}
});
// 优惠券服务
export const couponService = new Proxy(http, {
	get(target, prop) {
		const { apiDomain } = getEnvVar();
		// 构建工具不支持在build的阶段生成对应的包，改为了在代码中分捡的方式
		// http.defaults.baseURL = `${API_DOMAIN}${API_PREFIX}`;
		http.defaults.baseURL = apiDomain;
		return target[prop];
	}
});
export default http;
