.page-loading{
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 999;
	&[data-jump-page="true"]{
		position: fixed;
		.loading-inner{
			width: 75px;
			height: 45px;
			&:before,
			&:after{
				width: 30px;
				height: 30px;
			}
		}
	}
	.loading-bg{
		width: 100%;
		height: 100%;
		background: #000;
		opacity: .5;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 0;
	}
	.loading-inner {
		width: 50px;
		height: 30px;
		position: absolute;
		top: 50%;
		left: calc(50% - 25px);
		perspective: 200px;
		z-index: 1;
		&:before,
		&:after {
			position: absolute;
			width: 20px;
			height: 20px;
			content: "";
			animation: jumping 0.5s infinite alternate;
			background: rgba(0, 0, 0, 0);
		}
		&:before {
			left: 0;
		}
		&:after {
			right: 0;
			animation-delay: 0.15s;
		}
	}
}
@keyframes jumping {
	0% {
		transform: scale(1) translateY(0px) rotateX(0deg);
		box-shadow: 0 0 0 rgba(0, 0, 0, 0);
	}

	100% {
		transform: scale(1.2) translateY(-25px) rotateX(45deg);
		background: #3bd5f7;
		box-shadow: 0 25px 40px #3bd5f7;
	}
}
