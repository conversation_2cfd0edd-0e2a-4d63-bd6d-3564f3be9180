
import { useState, useEffect } from 'preact/hooks';
import './style.less';
import { fetchList } from '@stores';
import { IContent, IContentList, IEnvMap } from '@types';
import { <PERSON>erP<PERSON>, Footer, PageHeader, ContentCard } from '@components';
import IndexSwiper from './index-swiper';
import LastNews from './LastNews';
import { trackBiEvent, gtmEvent, adsComponentsInit, appendCreativeIdToHref, getFormattedDate } from '@utils';
import cls from 'classnames';
import LazyImage from '@components/lazy-img';
import { getScaleImgSrc } from '@utils';
import LeftCard from './LeftCard';
import RightCard from './RightCard';
const adsMap: any = {};
let category = '';
// 每次拉取条数
const PAGE_SIZE = 12;
let nowPage = 3;
let nowList: Array<IContent>;
// 是否启用拉取
let useFetch = false;
interface IProps {
	seoEnvMap: IEnvMap;
	seoActiveCategory: string;
	seoCategoryListData: any;
	seoNewsList: any;
	seoCategoryDataList: any


}

export default function App(props: IProps) {
	const { seoEnvMap, seoActiveCategory, seoCategoryListData, seoNewsList, seoCategoryDataList } = props;
	const { creativeId, lang, imgDomain, theme, timeEnable, hostname } =
		seoEnvMap;
	const { logo } = theme;
	const [newList, setNewList] = useState([])

	const showTime = timeEnable && timeEnable.enable === false ? false : true;
	// type[1.站点内容, 2.search广告]



	useEffect(() => {
		let result = []
		// console.log(seoCategoryDataList);

		for (let i = 0; i < seoCategoryDataList.length; i += 3) {
			// 合并前两个
			const merged = [seoCategoryDataList[i]];
			if (i + 1 < seoCategoryDataList.length) {
				merged.push(seoCategoryDataList[i + 1]);
			}
			result.push(merged);

			// 第三个单独放
			if (i + 2 < seoCategoryDataList.length) {
				result.push([seoCategoryDataList[i + 2]]);
			}
		}

		console.log(result);
		setNewList(result)
	}, [])

	const onContentClick = (type: number, title: string, category: number) => {
		if (type === 2) return;
		const params = {
			event_name: 'news_click',
			page_id: category
		}
		trackBiEvent('news_click', params);

		// 发送gtm事件: 内容点击事件
		gtmEvent('item_click', {
			event_label: title
		});
	}
	return (
		<section className="index-wrap-pc">
			<HeaderPc logo={logo} creativeId={creativeId} seoCategoryListData={seoCategoryListData} activeCategory={seoActiveCategory} seoEnvMap={seoEnvMap} />
			<div className={cls('page-warp')}>
				<div className={cls('index-page-card-warp')}>
					<div className="menu">
						{
							seoCategoryListData.map((item, index) => {
								let aHref = `/${lang}/list/${item.name}`;
								return (
									<>
										<a
											key={item}
											href={appendCreativeIdToHref(
												aHref,
												creativeId
											)}
											className={cls(
												"category-name",
												seoActiveCategory ===
												item &&
												"active"
											)}>
											<span className="category-name-text">
												{item.name}
											</span>
										</a>
									</>
								)

							})
						}

					</div>
					<div className="index-body-one">
						<div className="one-left">
							{
								seoNewsList.length > 0 && (


									<a
										className="featured-news-card"
										onClick={() => { onContentClick(seoNewsList[0].type, seoNewsList[0].title, seoNewsList[0].category[0]) }} href={appendCreativeIdToHref(`/${lang}/${seoNewsList[0].path || seoNewsList[0].id}`, creativeId)}>

										<LazyImage
											className="featured-image"
											src={getScaleImgSrc(seoNewsList[0], imgDomain, '580*312')} alt={seoNewsList[0].title} />
										<div className="news-overlay">
											<div className="news-des">
												<div className="news-meta">
													{seoCategoryDataList[0].category_name}
													{showTime && <span className="news-time"> | {getFormattedDate(seoNewsList[0].published_time)}</span>}
												</div>
												<h3 className="news-title">
													{seoNewsList[0].title}
												</h3>
											</div>
										</div>
									</a>
								)
							}
						</div>
						<div className="one-right">
							{
								seoNewsList.slice(1, 3).map((item, index) => {
									return (
										<div>
											<a
												className={cls('right-card')}
												onClick={() => { onContentClick(item.type, item.title, item.category[0]) }}
												href={appendCreativeIdToHref(`/${lang}/${item.path || item.id}`, creativeId)}
											>
												<div className={cls('right-card-title')}>
													<div className={cls('right-card-title-text')}>
														{item.title}
													</div>
													<div className={cls('right-card-abs')}>
														{item.abstract}
													</div>
													{showTime && <div className={cls('right-card-title-time')}>
														{getFormattedDate(item.published_time)}
													</div>}

												</div>
												<LazyImage
													className='right-card-img'
													src={getScaleImgSrc(item, imgDomain, '170*100')} alt={item.title} seoEnvMap={seoEnvMap} />

											</a>
											{index===0 && <div className={'svg-warp'}>
												<svg xmlns="http://www.w3.org/2000/svg" width="560" height="2" viewBox="0 0 560 2" fill="none">
													<path d="M1.00012 1H559" stroke="#DDDDDD" stroke-width="2" stroke-linecap="round" />
												</svg>
											</div>}
										</div>

									)

								})
							}
						</div>
					</div>
					<div className="index-last-news">
						{seoNewsList.length > 7 && <LastNews seoEnvMap={seoEnvMap} seoNewsList={seoNewsList.slice(3, 7)} title={'Latest News'} />}
					</div>
					<div >

						{
							newList.map((list, index) => {
								if (index % 2 === 0) {
									return (
										<div className="index-body-two">

											<LeftCard key={index} seoEnvMap={seoEnvMap} seoNewsList={
												index === 0
													? list[0]?.listData?.slice(7, 11) || []
													: list[0]?.listData?.slice(0, 4) || []
											} title={list[0].category_name} />
											<RightCard key={index} seoEnvMap={seoEnvMap} seoNewsList={list[1].listData.slice(0, 5)} title={list[1].category_name} />
										</div>
									)
								} else {
									return <div className="index-body-th">
										<LastNews seoEnvMap={seoEnvMap} title={list[0].category_name} seoNewsList={list[0].listData.slice(0, 4)} />;
									</div>
								}
								return null;
							})
						}
					</div>
				</div>
			</div>
			<Footer
				creativeId={creativeId}
				lang={lang}
				logo={logo}
				hostname={hostname}
			/>
		</section >
	)
}
