@import "../../../assets/css/config.less";

.about-text-info {
	.about-text-content {
		text-transform: capitalize;
		padding-top: var(--header_height);
		max-width: 896px;
		min-height: calc(100vh - 138px);
		margin: 0 auto;

		.content-header {
			color: #000;
			font-size: 36px;
			font-weight: 800;
			padding: 45px 0 32px;
			text-transform: capitalize;
		}

		.content-container {
			line-height: 1.5;
			font-size: 18px;
		}
	}

	.mobile-inner {
		min-height: calc(100vh - 203px - 176px);

		.content-container {
			a {
				color: var(--a_color);
			}
			margin: 0 16px;
			font-size: 18px;
		}
	}

	.content-mobile {
		margin-top: var(--header_m_height);

		.content-header {
			padding: 10px 16px;
			padding-left: 15px;
			font-size: 24px;
			font-weight: 700;
		}
	}
}
body[data-system="mobile"] {
	.about-text-info {
		padding-top: 84px;
		.mobile-inner {
			.content-mobile {
				text-align: center;
			}
			.content-container {
				font-size: 16px;
				margin: 0 16px;
				padding: 0 16px;
			}
		}
	}
}
