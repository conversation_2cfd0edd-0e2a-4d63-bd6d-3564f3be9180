import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import { LazyImg, LinkOrA } from '@components';
import { getFormattedDate, getFormattedNumberDate, getScaleImgSrc } from '@utils';
import { IContent } from '@types';
import cls from 'classnames';

interface IProps {
  item: IContent
  lang: string
  imgDomain: string
}
export default function Scroll(props: IProps) {
  const { item, lang, imgDomain } = props;
  return (
    <LinkOrA path={`/${lang}/${item.path || item.id}`} className="scroll-item">
      <LazyImg src={getScaleImgSrc(item, imgDomain, '534*300')} alt={item.title} />
      <div className="scroll-item-info">
        <div className={cls("title",item.abstract ? "title-active" : "")}>{item.title}</div>
        {
          item.abstract && (
            <div className="desc">{item.abstract}</div>
          )
        }
      </div>
    </LinkOrA>
  )
}
