@import "../../assets/css/config";
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote,
dl, dt, dd, ul, ol, li, pre,
fieldset, lengend, button, input, textarea,
th, td {
	margin: 0;
	padding: 0;
}

header, nav, article, section, canvas, footer {
	display: block;
}

body, button, input, select, textarea {
	font: 12px/1.5 "Source Han Sans SC-Regular", tahoma, helvetica, arial, "\5b8b\4f53", sans-serif;
	outline: none;
}

h1, h2, h3 {
	font-weight: normal;
	font-family: 'Poppins', sans-serif;
}
h4, h5, h6 {
	font-size: 100%;
}

address, cite, dfn, em, var {
	font-style: normal;
}

small {
	font-size: 12px;
}

ul{
	list-style: none;
}

a,a:link,a:visited,a:hover,a:active{
	text-decoration: none;
}

abbr[title], acronym[title] {
	border-bottom: 1px dotted;
	cursor: help;
}

q:before, q:after {
	content: '';
}

legend {
	color: #000;
}

fieldset, img {
	border: none;
}

button, input, select, textarea {
	font-size: 100%;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
	width: 100%;
}

hr {
	border: none;
	height: 1px;
}
*{
	box-sizing: border-box;
}
html, body{
	min-height: 100%;
	font-family: "Source Han Sans SC";
}
body{
	background: var(--bg_color);
	&[system]{
		display: block;
	}
}

body[system="pc"], body[system="pc"] *{
	/* 自定义滚动条样式 */
	::-webkit-scrollbar-track {
		border-radius: 5px;
		background-color: #f3f3f3;
	}
	::-webkit-scrollbar-track-piece {
		display: none;
	}
	::-webkit-scrollbar {
		background: transparent;
		width: 10px;
		height: 10px;
	}
	::-webkit-scrollbar-thumb {
		border-radius: 5px;
		background-color: #e1e1e1;
		width: 6px;
		min-height: 50px;
		border: 2px solid transparent;
		background-clip: content-box;
	}

	// 火狐修改滚动条: 以下为firefox特有属性
	scrollbar-width: thin;
	scrollbar-color: #e1e1e1 #f3f3f3;
}


.page-warp {
	// padding: 24px;
	background-color: var(--theme-bg-color);
	border-radius: var(--theme-border-radius);
	max-width: 1200px;
	margin:  84px auto 52px;
}
.page-mobile-warp {
	padding: 10px 17px 40px;
	background-color: var(--theme-bg-color);
	width: 100%;
}