import { useState, useEffect } from 'preact/hooks';
import { ContentList, BarHeader, HeaderMobile, ContentCard, Footer, LinkOrA, LazyImg } from '@components'
import cls from 'classnames';
import { fetchList } from '@stores';
import { trackBiEvent, gtmEvent, adsComponentsInit, appendCreativeIdToHref, getFormattedNumberDate, getScaleImgSrc, getFormattedDate } from '@utils';
import './style.less';

import { IContent, IContentList, IEnvMap } from '@types';
import LazyImage from '@components/lazy-img';
import nosearch from '@assets/images/nosearch.png';
import { getLocale } from '@utils/locales';

// 每次拉取条数
const PAGE_SIZE = 12;
let nowPage = 3;
let nowList: Array<IContent>;
// 是否启用拉取
let useFetch = false;
let category = '';
const adsMap: any = {};
const recommendAds = [
	{
		zone_key: '300x100_mobile_index_content_bottom_1',
		position: 1,
		width: 300,
		height: 100
	}
]
// import { appendCreativeIdToHref } from '@utils';
// 主域使用的site id
const MAIN_SITE_ID = '24786993';

interface IProps {
	seoEnvMap: IEnvMap;
	seoSearchResult: string;
	seoSearchResultList: Array<IContent>,
	seoCategoryListData: Array<any>
	seoCategoryListItemData: Array<IContent>;

}
export default function App(props: IProps) {
	const { seoCategoryListData, seoActiveCategory, seoEnvMap, seoSearchResultList, seoSearchResult } = props;
	const { zoneMap, siteId, noHeader, isMobile, theme, imgDomain, creativeId, lang } = seoEnvMap;
	const showTime = seoEnvMap && seoEnvMap.timeEnable && seoEnvMap.timeEnable.enable === false ? false : true;

	const { logo } = theme || {};
	const [listData, setListData] = useState<Array<IContent>>(seoSearchResultList);
	const [activeCategory, setActiveCategory] = useState(seoActiveCategory);
	const [loading, setLoading] = useState(false);
	const [time, setTime] = useState(null);
	const [nextData, setNextData] = useState([]);
	const renderAdsDOM = (id: string, sizeW: number, sizeH: number) => {
		const adsId = `ads-dom-${id}`;
		if (!adsMap[adsId]) {
			adsMap[adsId] = {
				width: sizeW,
				height: sizeH,
				zoneId: zoneMap[id],
			};
		}
		return (
			<div data-ads-id={adsId}></div>
		);
	};
	const [inputValue, setInputValue] = useState(seoSearchResult);

	const saveValue = (e: String) => {
		setInputValue(e)
		// console.log(e);

	}
	const showSearchResult = () => {
		if (inputValue) {
			window.location.href = `/${lang}/search-result?search_result=${inputValue}`
		}
	}
	const inputKeyDown = (e: any) => {
		if (inputValue && e.key === 'Enter') {
			window.location.href = `/${lang}/search-result?search_result=${inputValue}`
		}
	}
	useEffect(() => {
		window.addEventListener('scroll', function () {
			if (!useFetch && window.innerHeight + window.scrollY + 100 > document.body.offsetHeight) {
				nowPage = nowPage + 1;
				useFetch = true;
				setLoading(true);
				fetchList({
					page_size: PAGE_SIZE,
					page_index: nowPage,
					category: 'seoCategoryListData[0]',
					last_published_time: nextData[nextData.length - 1]?.published_time,
					last_id: nextData[nextData.length - 1]?.id,
					site_id: siteId
				}).then((res: IContentList) => {
					const { data } = res;
					nowList = listData.concat(data)
					// console.log(nowList, 'nowList')
					setNextData(data)
					setListData(prevListData => {
						const nowList = prevListData.concat(data);
						// console.log(nowList, 'nowList');
						return nowList; // Return the new array for the updated state
					});
					// 当存在数据 且 当前不为最后一页
					if (data && data.length === PAGE_SIZE) {
						useFetch = false;
					}
				}).finally(() => {
					setLoading(false);
				});
			}
		});
	}, []);
	// 发送事件
	const sendEvent = (event_name: string) => {
		const params: any = {
			event_name,
			page_id: category
		}
		if (event_name === 'page_leave') {
			params.page_stayduration = (new Date().getTime() - time) / 1000
		}
		trackBiEvent(event_name, params);
	}

	useEffect(() => {
		setLoading(true);
		setTime(new Date().getTime());
	}, []);

	// 绑定埋点事件
	useEffect(() => {
		if (!loading) {
			return
		}
		sendEvent('page_reach');
	}, [loading])


	return (
		<div className="search-result-page">
			<div className="close-search">
				<LinkOrA path={`/${lang}`}  >
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="black" />
					</svg>
				</LinkOrA>
			</div>
			<div className="search-result-content">
				<div className='header-content-input'>
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 4.00001C8.77609 4.00001 7.12279 4.68483 5.90381 5.90382C4.68482 7.1228 4 8.7761 4 10.5C4 12.2239 4.68482 13.8772 5.90381 15.0962C7.12279 16.3152 8.77609 17 10.5 17C12.2239 17 13.8772 16.3152 15.0962 15.0962C16.3152 13.8772 17 12.2239 17 10.5C17 8.7761 16.3152 7.1228 15.0962 5.90382C13.8772 4.68483 12.2239 4.00001 10.5 4.00001ZM2 10.5C2.00012 9.1446 2.32436 7.80887 2.94569 6.60427C3.56702 5.39966 4.46742 4.3611 5.57175 3.57525C6.67609 2.78939 7.95235 2.27902 9.29404 2.08672C10.6357 1.89442 12.004 2.02576 13.2846 2.46979C14.5652 2.91382 15.7211 3.65766 16.6557 4.63925C17.5904 5.62084 18.2768 6.81171 18.6576 8.11252C19.0384 9.41333 19.1026 10.7864 18.8449 12.117C18.5872 13.4477 18.015 14.6975 17.176 15.762L20.828 19.414C21.0102 19.6026 21.111 19.8552 21.1087 20.1174C21.1064 20.3796 21.0012 20.6304 20.8158 20.8158C20.6304 21.0012 20.3796 21.1064 20.1174 21.1087C19.8552 21.111 19.6026 21.0102 19.414 20.828L15.762 17.176C14.5086 18.164 13.0024 18.7792 11.4157 18.9511C9.82905 19.123 8.22602 18.8448 6.79009 18.1482C5.35417 17.4517 4.14336 16.3649 3.29623 15.0123C2.44911 13.6597 1.99989 12.096 2 10.5ZM9.5 7.00001C9.5 6.73479 9.60536 6.48044 9.79289 6.2929C9.98043 6.10537 10.2348 6.00001 10.5 6.00001C11.6935 6.00001 12.8381 6.47412 13.682 7.31803C14.5259 8.16194 15 9.30654 15 10.5C15 10.7652 14.8946 11.0196 14.7071 11.2071C14.5196 11.3947 14.2652 11.5 14 11.5C13.7348 11.5 13.4804 11.3947 13.2929 11.2071C13.1054 11.0196 13 10.7652 13 10.5C13 9.83697 12.7366 9.20108 12.2678 8.73224C11.7989 8.2634 11.163 8.00001 10.5 8.00001C10.2348 8.00001 9.98043 7.89465 9.79289 7.70712C9.60536 7.51958 9.5 7.26523 9.5 7.00001Z" fill="black" />
					</svg>
					<input
						type="text"
						value={inputValue}
						className='header-input'
						placeholder={getLocale(lang, "search_input")}
						onChange={(e) => saveValue(e.target.value)}
						onKeyDown={inputKeyDown}
					/>
				</div>
				<div className="search-result-card-list" style={{ background: listData.length > 0 ? '' : 'none' }}>
					{
						listData && listData.length > 0 && seoSearchResultList ? <div className="search-result-list">
							{
								listData.map((item, index) => {
									return (
										<LinkOrA path={`/${seoEnvMap.lang}/${item.path || item.id}`} className="category-item">

											<LazyImg src={getScaleImgSrc(item, seoEnvMap.imgDomain, '263*184')} alt={item.title} />
											<div className="category-item-content">

												<div className="content-title">{item.title}</div>

												{
													showTime && (
														<div className="content-two">{getFormattedDate(item.published_time)}</div>
													)
												}
											</div>

										</LinkOrA>
									)
								})
							}
						</div> :

							<div className="no-data-content">
								<div className="no-data">
									<LazyImage src={nosearch} alt="no-data" />
									<span>no search result yet</span>
								</div>
							</div>

					}
				</div>
			</div>
		</div>
	);
}
