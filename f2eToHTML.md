## 将前端静态资源打到html步骤

### 1. webpack-common.config.js
- 将webpack-common.config.js里面的`externals`配置移除，因为我们需要将静态资源打到html中，而不是通过外链引入。
### 2. webpack-build.config.js
- 给new CopyWebpackPlugin增加
```js
{
					from: path.join(__dirname, '/src/dist/react.production.min.js'),
					to: './dist/'
				},
				{
					from: path.join(__dirname, '/src/dist/react-dom.production.min.js'),
					to: './dist/'
				}

```
将生产环境的依赖打到前端
### 3. utils/ssr.tsx
- 删除之前的CDN链接
- 将每个页面需要的css js都通过ssr.tsx传入并且使用
