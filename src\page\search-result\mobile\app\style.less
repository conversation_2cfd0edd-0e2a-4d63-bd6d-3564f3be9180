@import '../../../../assets/css/config';

.search-result-page {
	width: 100vw;
	min-height: 100vh;
	background: var(--theme-bg-color);
	padding: 40px 16px 20px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	gap: 18px;
	align-items: flex-start;

	.close-search {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
		padding-right: 24px;
	}

	.search-result-content {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		gap: 18px;

		.header-content-input {
			border-bottom: 2px solid var(--theme-color);
			display: flex;
			width: 100%;
			height: 64px;
			padding-left: 12px;
			align-items: center;
			gap: 14px;
			flex-shrink: 0;

			input {
				color: #000;
				font-family: Inter;
				font-size: 16px;
				font-style: normal;
				font-weight: 500;
				line-height: normal;
				text-transform: capitalize;
				width: 100%;
				border: 0;
				background: none;
			}
		}

		.search-result-card-list {
			width: 100%;

			.search-result-list {
				display: flex;
				width: 100%;
				flex-direction: column;
				align-items: flex-start;
				gap: 8px;

				.category-item {
					height: 124px;
					align-self: stretch;
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					position: relative;

					img {
						border-radius: 8px;
						width: 30%;
						aspect-ratio: 1 / 1;
						flex-shrink: 0;
						object-fit: cover;
					}

					.category-item-content {
						position: absolute;
						bottom: -4px;
						right: 6px;
						width: 75%;
						border-radius: 8px;
						background: #FFF;
						backdrop-filter: blur(11px);
						display: flex;
						padding: 12px 15px;
						flex-direction: column;
						align-items: flex-start;
						gap: 10px;
						height: 101px;
						justify-content: center;

						.content-title {
							align-self: stretch;
							color: #000;
							font-family: Inter;
							font-size: 16px;
							font-style: normal;
							font-weight: 500;
							line-height: normal;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							overflow: hidden;
							/*! autoprefixer: off */
							-webkit-box-orient: vertical;
							// flex: 1;
						}

						.content-two {
							color: #A8A8A8;
							font-family: Inter;
							font-size: 13px;
							font-style: normal;
							font-weight: 500;
							line-height: normal;
							align-self: stretch;
							
						}
					}

				}
			}

			.no-data-content {
				padding-top: 146px;

				.no-data {
					width: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 6px;

					img {
						width: 103px;
						height: 103px;
						object-fit: cover;
					}

					span {
						color: var(--theme-color);
						font-family: Inter;
						font-size: 24px;
						font-style: normal;
						font-weight: 800;
						line-height: normal;
						text-transform: capitalize;
						margin-top: -15px;
					}
				}
			}
		}
	}
}