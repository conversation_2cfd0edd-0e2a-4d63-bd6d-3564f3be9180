import { useState } from 'preact/hooks';
import cls from 'classnames';
import searchUrl from '@assets/images/search.svg'
import './style.less';
import { appendCreativeIdToHref } from '@utils';
import { IEnvMap } from '@types';
import { Language } from '@components';
import defaultLogoUrl from '@assets/images/logoDefault.png'
import { getLocale } from '@utils/locales';

interface IProps {
	logo?: string,
	creativeId: string;
	searchResult?: string;
	seoCategoryListData: Array<any>;
	activeCategory?: string;
	seoEnvMap: IEnvMap
}

export default function HeaderPc(props: IProps) {
	const { logo, creativeId, searchResult, seoCategoryListData, activeCategory, seoEnvMap } = props
	const [searchValue, setSearchValue] = useState(searchResult || '');
	const { lang } = seoEnvMap;

	const handleSearch = () => {
		if (searchValue) {
			window.location.href = `/${lang}/search-result?search_result=${searchValue}`
		}
	}
	const inputKeyDown = (e: any) => {
		if (searchValue && e.key === 'Enter') {
			window.location.href = `/${lang}/search-result?search_result=${searchValue}`
			setSearchValue('')
		}
	}
	const inputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchValue(e.target.value)
	}


	return (
		<div className="header-component">
			<div className="header-wrap">
				{/* 直接写路径 */}
				<a className="title" style={{
					backgroundImage: logo
						? `url(${logo})`
						: `url(${defaultLogoUrl})`,
				}} href={appendCreativeIdToHref(`/${lang}`, creativeId)} />
				<div className={cls('header-right')}>
					<div className={cls('header-search')}>
						{/* <img src={searchUrl} alt="" onClick={handleSearch}/> */}
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M7.19573 15.433C5.94875 13.8254 5.36106 11.803 5.55223 9.77746C5.74339 7.75189 6.69905 5.87522 8.22478 4.52925C9.75051 3.18328 11.7317 2.46912 13.7653 2.53205C15.7989 2.59498 17.7321 3.43027 19.1717 4.868C20.6126 6.30673 21.4506 8.24072 21.515 10.2759C21.5795 12.311 20.8655 14.2941 19.5185 15.8211C18.1716 17.3481 16.293 18.304 14.2658 18.4941C12.2385 18.6841 10.215 18.094 8.60773 16.844L8.56473 16.889L4.32273 21.132C4.22982 21.2249 4.11952 21.2986 3.99813 21.3489C3.87673 21.3992 3.74663 21.4251 3.61523 21.4251C3.48384 21.4251 3.35373 21.3992 3.23233 21.3489C3.11094 21.2986 3.00064 21.2249 2.90773 21.132C2.81482 21.0391 2.74112 20.9288 2.69084 20.8074C2.64055 20.686 2.61467 20.5559 2.61467 20.4245C2.61467 20.2931 2.64055 20.163 2.69084 20.0416C2.74112 19.9202 2.81482 19.8099 2.90773 19.717L7.15073 15.475L7.19573 15.433ZM9.27173 6.283C8.70713 6.83848 8.2581 7.50025 7.95053 8.23014C7.64297 8.96002 7.48295 9.74358 7.47973 10.5356C7.4765 11.3277 7.63013 12.1125 7.93174 12.8449C8.23335 13.5772 8.67698 14.2426 9.23704 14.8027C9.7971 15.3628 10.4625 15.8064 11.1949 16.108C11.9272 16.4096 12.7121 16.5632 13.5041 16.56C14.2961 16.5568 15.0797 16.3968 15.8096 16.0892C16.5395 15.7816 17.2013 15.3326 17.7567 14.768C18.8668 13.6397 19.4861 12.1184 19.4796 10.5356C19.4732 8.9528 18.8416 7.43664 17.7223 6.31741C16.6031 5.19818 15.0869 4.56655 13.5041 4.5601C11.9213 4.55365 10.4 5.17292 9.27173 6.283Z" fill="#353535" />
						</svg>
						<input type="text" placeholder={getLocale(lang,"search")} value={searchValue} onChange={inputChange} onKeyDown={inputKeyDown} />
					</div>
					<Language seoEnvMap={seoEnvMap} />
				</div>

			</div>
		</div>
	)
}
