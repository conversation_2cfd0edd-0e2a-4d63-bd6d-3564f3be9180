import http, { basicService, couponService } from '@http';
import { IContentList } from 'typings/types';

interface IFetchListParams {
	category?: string;
	page_size?: number;
	page_index?: number;
	last_published_time?: string;
	last_id?: string;
	search_val?: string;
	keyword?: string;
	site_id?: string
}
export const fetchList = (params: IFetchListParams): Promise<IContentList> => {
	return new Promise((resolve, reject) => {
		basicService.get('/summary/list', { params }).then((res: any) => {
			resolve(res || {});
		})
		.catch((err: any) => {
			reject(err);
		});
	})
} 


export const fetchSearchLists = (params: IFetchListParams): Promise<IContentList> => {
	return new Promise((resolve, reject) => {
		basicService.get('/summary/listForSearch', { params }).then((res: any) => {
			resolve(res || {});
		})
		.catch((err: any) => {
			reject(err);
		});
	})
} 


export const fetchSearchList = (params: any): Promise<any> => {
	return new Promise((resolve, reject) => {
		// https://apicpm.yeahtargeter.com/search.json
		http.get('https://search.gamebridge.games/search.json', { params }).then((res: any) => {
			if (res && res.organic) {
				resolve({
					list: res.organic,
					total: res.organic.length
				});
			} else {
				resolve({
					list: [],
					total: 0
				});
			}
		})
		.catch((err: any) => {
			reject(err);
		});
	})
}

interface IFetchCodeRes {
	data: {
		shop_logo: string,
		reward: string,
		reward_icon: string,
		reward_scene: string,
		redeem_code: string,
		deep_link: string,
		coupon_instruction: string,
		disclaimer: string
	},
	success: boolean
}
export const fetchCopyCode = (params: any): Promise<IFetchCodeRes> => {
	return couponService.get('/shop', { params })
}
