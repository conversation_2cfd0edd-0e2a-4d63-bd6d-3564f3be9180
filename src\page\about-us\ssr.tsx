import renderToString from 'preact-render-to-string';
import App from './app';
import { getSsrHtml } from '@utils/ssr';
import { IContent, IEnvMap,IF2eFiles } from '@types';

interface IProps {
	seoEnvMap: IEnvMap;
	categoryListData: string[];
	mainDomain: string;
}
export default function SSR(env: string, props: IProps ,f2eFiles:IF2eFiles) {
	const content = renderToString(
		<App {...props}/>
	);
	return getSsrHtml(env, 'about-us', content, props,f2eFiles);
}
