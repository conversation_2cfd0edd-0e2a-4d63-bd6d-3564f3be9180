import cls from 'classnames';
import './style.less';

interface IProps {
	className?: string;
	style?: React.CSSProperties;
	jumpPage?: boolean;
	disabledBG?: boolean;
}
export default function PageLoading(props: IProps) {
	const { style, className, jumpPage, disabledBG } = props;
	return (
		<section style={style} className={cls('page-loading', className)} data-jump-page={jumpPage ? 'true' : undefined}>
			<div className="loading-inner"></div>
			{
				disabledBG ? null : <div className="loading-bg"/>
			}
		</section>
	);
}
