.spinner-index {
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	height: 32px;

	.spinner {
		width: 16px;
		height: 16px;
		border-radius: 50%;
		border: 2px solid #42c2e2;
		/* 背景颜色 */
		border-top: 2px solid white;
		/* 动画的颜色 */
		animation: spin 1s linear infinite;
		/* 旋转动画 */
	}

	.no-more {
		font-size: 14px;
		color: #666;
		text-align: center;
		line-height: 40px;
	}
}



@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}