.classified-news {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    .title {
        flex: 1 0 0;
        color: #080F18;
        font-family: Inter;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        text-transform: capitalize;
    }

    .top {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 16px;
        margin-top: 8px;

        .banner {
            border-radius: 8px;
            width: 100%;
            aspect-ratio: 1.58/1;
            flex-shrink: 0;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            position: relative;

            .category-banner {
                display: flex;
                align-items: flex-start;
                gap: 8px;
                flex-direction: row;
                position: absolute;
                bottom: 72px;
                left: 8px;

                .category,
                .content-two {
                    color: #FFF;
                    font-family: Inter;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 24px;
                    /* 171.429% */
                    text-transform: capitalize;
                }

                .content-two {
                    border-left: 1px solid #FFF;
                    padding-left: 6px;
                }
            }

            .banner-title {
                color: #FFF;
                font-family: Inter;
                font-size: 18px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                text-transform: capitalize;
                width: 90%;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                overflow: hidden;
                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
                position: absolute;
                bottom: 26px;
                left: 8px;
            }

            .banner-desc {
                color: #FFF;
                font-family: Inter;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-transform: capitalize;
                position: absolute;
                bottom: 9px;
                left: 8px;
            }
        }

        .top-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            align-self: stretch;
        }
    }
}