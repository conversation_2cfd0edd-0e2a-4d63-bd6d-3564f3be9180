import "./style.less";
import { appendCreativeIdToHref } from "@utils";
import defaultLogo from "../../assets/images/logoDefault.png";
import { getLocale } from "@utils/locales";

interface IProps {
	creativeId: string;
	lang: string;
	logo: string;
	isMobile?: boolean;
	hostname: string;

}

export default function Footer(props: IProps) {
	const { creativeId, lang, logo, isMobile, hostname } = props;
	const handleShare = (site: string) => {
		let newsUrlList = encodeURIComponent(`https://${hostname}`);
		if (site === "instagram") {
			// Instagram 通常需要使用应用内部的分享功能
			// 这里只能打开 Instagram 的创建页面
			window.open(`https://www.instagram.com/create/`);
		}
		if (site === "twitter") {
			window.open(`https://twitter.com/intent/tweet?url=${newsUrlList}`);
		}
		if (site === "facebook") {
			window.open(
				`https://www.facebook.com/sharer/sharer.php?u=${newsUrlList}`
			);
		}
		if (site === "tiktok") {
			window.open('https://www.tiktok.com/');
		}
	};
	return (
		<>
			{!isMobile ? (
				<div className="footer">
					<div className="foot-box">
						<a
							className="logo"
							href={appendCreativeIdToHref(
								`/${lang}`,
								creativeId
							)}
							style={{
								backgroundImage: `url(${logo || defaultLogo})`,
							}}
						/>
						<div className="footer-warp">
							<div className="left">
								{getLocale(
									lang,
									"Copyright © 2025 All Rights Reserved"
								)}
							</div>
							<div className="right">
								<a
									href={appendCreativeIdToHref(
										`/${lang}/about-us`,
										creativeId
									)}
								>
									{getLocale(lang, "about_us")}
								</a>
								<a
									href={appendCreativeIdToHref(
										`/${lang}/privacy-policy`,
										creativeId
									)}
								>
									{getLocale(lang, "privacy_policy")}
								</a>
								<a
									href={appendCreativeIdToHref(
										`/${lang}/contact-me`,
										creativeId
									)}
								>
									{getLocale(lang, "contact_us")}
								</a>
								<a
									href={appendCreativeIdToHref(
										`/${lang}/terms-of-use`,
										creativeId
									)}
								>
									{getLocale(lang, "terms_of_use")}
								</a>
							</div>
						</div>
					</div>
				</div>
			) : (
				<div className="m-footer">
					<div className="top">
						<a
							href={appendCreativeIdToHref(
								`/${lang}/about-us`,
								creativeId
							)}
						>
							{getLocale(lang, "about_us")}
						</a>
						<div className="line" />
						<a
							href={appendCreativeIdToHref(
								`/${lang}/privacy-policy`,
								creativeId
							)}
						>
							{getLocale(lang, "privacy_policy")}
						</a>
						<div className="line" />
						<a
							href={appendCreativeIdToHref(
								`/${lang}/contact-me`,
								creativeId
							)}
						>
							{getLocale(lang, "contact_us")}
						</a>
						<div className="line" />
						<a
							href={appendCreativeIdToHref(
								`/${lang}/terms-of-use`,
								creativeId
							)}
						>
							{getLocale(lang, "terms_of_use")}
						</a>
					</div>
					<div className="footer-mobile-social-icons">
						<svg
							onClick={() => handleShare("facebook")}
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
						>
							<path
								d="M12.8349 2.43359C7.11279 2.43359 2.48486 7.09109 2.48486 12.8427C2.48486 18.0325 6.27001 22.35 11.2232 23.1336V15.859H8.59136V12.8427H11.2232V10.551C11.2232 7.93388 12.7757 6.49967 15.1266 6.49967C16.2651 6.49967 17.448 6.70667 17.448 6.70667V9.26459H16.1469C14.8605 9.26459 14.4613 10.063 14.4613 10.891V12.8427H17.3297L16.8714 15.859H14.4613V23.1336C19.4145 22.35 23.1996 18.0473 23.1996 12.8427C23.1996 7.09109 18.5717 2.43359 12.8496 2.43359H12.8349Z"
								fill="#ABAEB3"
							/>
						</svg>
						<svg
							onClick={() => handleShare("instagram")}
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
						>
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M15.976 3.03369C17.3522 3.03369 18.672 3.58037 19.6451 4.55346C20.6182 5.52655 21.1648 6.84635 21.1648 8.22251V16.5246C21.1648 17.9008 20.6182 19.2206 19.6451 20.1937C18.672 21.1667 17.3522 21.7134 15.976 21.7134H7.67392C6.29776 21.7134 4.97797 21.1667 4.00488 20.1937C3.03178 19.2206 2.48511 17.9008 2.48511 16.5246V8.22251C2.48511 6.84635 3.03178 5.52655 4.00488 4.55346C4.97797 3.58037 6.29776 3.03369 7.67392 3.03369H15.976ZM15.976 5.10922H7.67392C6.84823 5.10922 6.05635 5.43722 5.4725 6.02108C4.88864 6.60493 4.56063 7.39681 4.56063 8.22251V16.5246C4.56063 17.3503 4.88864 18.1422 5.4725 18.726C6.05635 19.3099 6.84823 19.6379 7.67392 19.6379H15.976C16.8017 19.6379 17.5936 19.3099 18.1775 18.726C18.7613 18.1422 19.0893 17.3503 19.0893 16.5246V8.22251C19.0893 7.39681 18.7613 6.60493 18.1775 6.02108C17.5936 5.43722 16.8017 5.10922 15.976 5.10922ZM11.825 8.22251C12.9259 8.22251 13.9817 8.65985 14.7602 9.43832C15.5387 10.2168 15.976 11.2726 15.976 12.3736C15.976 13.4745 15.5387 14.5303 14.7602 15.3088C13.9817 16.0873 12.9259 16.5246 11.825 16.5246C10.724 16.5246 9.66821 16.0873 8.88974 15.3088C8.11126 14.5303 7.67392 13.4745 7.67392 12.3736C7.67392 11.2726 8.11126 10.2168 8.88974 9.43832C9.66821 8.65985 10.724 8.22251 11.825 8.22251ZM11.825 10.298C11.2745 10.298 10.7466 10.5167 10.3574 10.9059C9.96812 11.2952 9.74945 11.8231 9.74945 12.3736C9.74945 12.924 9.96812 13.4519 10.3574 13.8412C10.7466 14.2304 11.2745 14.4491 11.825 14.4491C12.3754 14.4491 12.9034 14.2304 13.2926 13.8412C13.6818 13.4519 13.9005 12.924 13.9005 12.3736C13.9005 11.8231 13.6818 11.2952 13.2926 10.9059C12.9034 10.5167 12.3754 10.298 11.825 10.298ZM16.4949 6.66586C16.7701 6.66586 17.0341 6.7752 17.2287 6.96982C17.4233 7.16443 17.5327 7.42839 17.5327 7.70363C17.5327 7.97886 17.4233 8.24282 17.2287 8.43743C17.0341 8.63205 16.7701 8.74139 16.4949 8.74139C16.2197 8.74139 15.9557 8.63205 15.7611 8.43743C15.5665 8.24282 15.4571 7.97886 15.4571 7.70363C15.4571 7.42839 15.5665 7.16443 15.7611 6.96982C15.9557 6.7752 16.2197 6.66586 16.4949 6.66586Z"
								fill="#ABAEB3"
							/>
						</svg>
						<svg
							onClick={() => handleShare("tiktok")}
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
						>
							<path
								d="M17.0848 6.45379C16.4014 5.67332 16.0247 4.67119 16.0248 3.63379H12.9348V16.0338C12.9114 16.705 12.6283 17.3409 12.1451 17.8073C11.6619 18.2737 11.0164 18.5342 10.3448 18.5338C8.92478 18.5338 7.74478 17.3738 7.74478 15.9338C7.74478 14.2138 9.40478 12.9238 11.1148 13.4538V10.2938C7.66478 9.83379 4.64478 12.5138 4.64478 15.9338C4.64478 19.2638 7.40478 21.6338 10.3348 21.6338C13.4748 21.6338 16.0248 19.0838 16.0248 15.9338V9.64379C17.2778 10.5436 18.7821 11.0264 20.3248 11.0238V7.93379C20.3248 7.93379 18.4448 8.02379 17.0848 6.45379Z"
								fill="#ABAEB3"
							/>
						</svg>
						<svg
							onClick={() => handleShare("twitter")}
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
						>
							<path
								d="M22.4849 7.16808C21.7782 7.48931 21.0164 7.7004 20.2272 7.80135C21.0348 7.31493 21.6589 6.54399 21.9526 5.61703C21.1908 6.07592 20.3465 6.39715 19.4562 6.5807C18.7312 5.79141 17.7124 5.33252 16.556 5.33252C14.3992 5.33252 12.6371 7.09466 12.6371 9.2698C12.6371 9.58185 12.6738 9.88472 12.7381 10.1692C9.47076 10.004 6.56139 8.43462 4.62487 6.05757C4.28529 6.63577 4.09256 7.31493 4.09256 8.0308C4.09256 9.39829 4.7809 10.6098 5.84552 11.2981C5.1939 11.2981 4.58816 11.1145 4.05585 10.8392V10.8667C4.05585 12.7757 5.41417 14.3727 7.21302 14.7306C6.63558 14.8893 6.02911 14.9113 5.4417 14.7948C5.69097 15.5772 6.17917 16.2618 6.83766 16.7524C7.49615 17.243 8.29182 17.5149 9.11282 17.5298C7.72116 18.6316 5.99605 19.2272 4.22105 19.2186C3.909 19.2186 3.59696 19.2002 3.28491 19.1635C5.0287 20.2832 7.10288 20.9348 9.32391 20.9348C16.556 20.9348 20.53 14.9325 20.53 9.72869C20.53 9.55431 20.53 9.38911 20.5208 9.21473C21.2918 8.66407 21.9526 7.96655 22.4849 7.16808Z"
								fill="#ABAEB3"
							/>
						</svg>
					</div>
					<div className="bottom">
						{getLocale(
							lang,
							"Copyright © 2025 All Rights Reserved"
						)}
					</div>
				</div>
			)}
		</>
	);
}
