.header-component-mobile {
	position: relative;
	padding: 0;
	height: fit-content;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
	gap: 20px;
	border-bottom: 1px solid rgba(17, 17, 17, 0.07);
	padding-bottom: 8px;

	.language {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		position: relative;
		padding: 0 4px;

		.language-close {
			position: absolute;
			top: 8px;
			right: 19px;
		}

		.language-title {
			margin-top: 52px;
			margin-bottom: 34px;
			color: #000;
			font-family: Inter;
			font-size: 18px;
			font-style: normal;
			font-weight: 500;
			line-height: normal;
			text-transform: capitalize;
		}

		.language-list {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			gap: 12px;

			.language-item {
				border-radius: 8px;
				background: #FFF;
				display: flex;
				padding: 18px 9px;
				justify-content: center;
				align-items: center;
				gap: 10px;
				flex: 1 0 0;
				width: 100%;
				height: 54px;

				.language-item-name {
					color: #000;
					font-family: Inter;
					font-size: 15px;
					font-style: normal;
					font-weight: 500;
					line-height: normal;
					text-transform: capitalize;
				}
			}

			.active {
				border: 2px solid var(--theme-color);
				background: var(--theme-color);

				.language-item-name {
					color: #FFF;
				}
			}
		}
	}


	.header-content {
		// position: fixed;
		width: 100%;
		top: 0;
		z-index: 2;
		padding: 0 11px;
		// border-bottom: 1px solid var(--border_color);
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 46px;


		.title {
			width: 60%;
			height: 46px;
			background-size: contain;
			background-position: center left;
			background-repeat: no-repeat;
		}

	}

	.header-content-input {
		width: 100%;
		position: relative;
		padding: 0 11px;

		// left:50%;
		// transform: translateX(-50%);
		.header-input {
			width: 100%;
			// line-height: 40px;
			border-radius: 58px;
			background: #FFF;
			border: none;
			padding: 10 60px;
			color: #000;
			font-family: Inter;
			font-size: 16px;
			font-style: normal;
			font-weight: 500;
			line-height: normal;
			text-transform: capitalize;
			border: 0;
		}

		.header-input::placeholder {
			color: #D0D0D0;
		}

		.input-search {
			position: absolute;
			left: 30px;
			top: 12px;
		}

		.input-delete {
			position: absolute;
			right: 30px;
			top: 6px;
		}

	}

	.header-content-category {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		gap: 30px;
		overflow: hidden;
		overflow-x: auto;
		padding: 0 11px;

		.category {
			color: #000;
			font-family: Inter;
			font-size: 18px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			text-transform: capitalize;
			padding: 0 0 10px;
			white-space: nowrap;
		}

		.category-active {
			border-bottom: 5px solid var(--theme-color);
			padding: 0 0 5px;
		}
	}
}

.lang-active{
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: var(--theme-bg-color);
	z-index: 5;
	padding: 20px 12px;

	.language{
		justify-content: flex-start;
		width: 100%;
		height: 100%;

		.language-list{
			max-height: calc(100% - 112px);
			overflow: hidden;
			overflow-y: auto;
		}
	}
}