.category-item {
    height: auto;
    align-self: stretch;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;

    img {
        border-radius: 8px;
        width: 30%;
        aspect-ratio: 1 / 1;
        flex-shrink: 0;
        object-fit: cover;
    }

    .category-item-content {
        position: absolute;
        bottom: -4px;
        right: 6px;
        width: 75%;
        border-radius: 8px;
        background: #FFF;
        backdrop-filter: blur(11px);
        display: flex;
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        // height: 101px;
        justify-content: center;

        .content-title {
            align-self: stretch;
            color: #000;
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            overflow: hidden;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            // flex: 1;
        }

        .content-two {
            color: #A8A8A8;
            font-family: Inter;
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            align-self: stretch;

        }
    }

}