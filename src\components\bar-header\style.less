.text-header{
	width: 100%;
	height: 58px;
	position: relative;
	padding: 14px 16px;
	line-height: 30px;
	overflow: hidden;
	h2{
		width: calc(100% - 20px);
		color: var(--current_category_color);
		font-weight: 600;
		font-size: 20px;
		line-height: 24px;
		position: relative;
		padding-left: 8px;
		&::after{
			content: "";
			display: block;
			width: 4px;
			height: 12px;
			position: absolute;
			left: 0;
			top: 6px;
			background-color: #FF2727;
		}
	}
	a{
		display: block;
		width: 20px;
		position: absolute;
		top: 14px;
		right: 16px;
		color: #979797;
	}
}
