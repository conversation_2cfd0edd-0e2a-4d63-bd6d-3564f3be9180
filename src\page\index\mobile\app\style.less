@import '../../../../assets/css/config';

.index-wrap-mobile {

	padding: 20px 12px 45px;
	background: var(--theme-bg-color);
	min-height: 100vh;
	width: 100%;

	.scroll-wrap {
		width: 100%;
		height: 286px;
		margin-top: 16px;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
		align-self: stretch;

		.scroll-item-title {
			flex: 1 0 0;
			color: #080F18;
			font-family: Inter;
			font-size: 24px;
			font-style: normal;
			font-weight: 700;
			line-height: normal;
			text-transform: capitalize;
		}

		.scroll-items {
			width: 100%;
			display: flex;
			flex-direction: row;
			height: 243px;
			align-items: center;
			gap: 16px;
			overflow: hidden;
			overflow-x: auto;
		}
	}

	.bottom-news{

		.list{
			margin-top: -16px;
		}
	}
}