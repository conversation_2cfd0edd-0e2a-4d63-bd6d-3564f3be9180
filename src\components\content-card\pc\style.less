.small-image-card{
	border: 1px solid var(--border_color);
	display: block;
	border-radius: var(--theme-border-radius);
	background-color: var(--theme-pc-content-card-bg);
	position: relative;
	.img {
		position: relative;
		width: 100%;
		padding-top: 59.4%;
		overflow: hidden;
		img {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			right: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			border-top-right-radius: var(--theme-border-radius);
			border-top-left-radius: var(--theme-border-radius);
		}
	}
	.content-card-pc-text-warp {
		padding: 18px; 
		.title {
			min-height: 48px;
			font-size: 16px;
			color: #000;
			font-weight: 600;
			margin-bottom: 12px ;
			display: -webkit-box;         /* 必须结合的用于支持多行截断 */
			-webkit-line-clamp: 2;        /* 限制显示的行数 */
			-webkit-box-orient: vertical; /* 设置盒子布局为垂直方向 */
			overflow: hidden;             /* 隐藏多余的文本 */
			text-overflow: ellipsis;      /* 使用省略号表示截断的文本 */
		}
		.time {
			font-size: 12px;
			color: #9F9F9F;
			font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
		}
	}
	
}

.big-image-card{
	border: 1px solid var(--border_color);
	display: block;
	border-radius: var(--theme-border-radius);
	
	.big-image-card-warp {
		width: 100%;
		padding-top: 45.02%;
		position: relative;
		overflow: hidden;
		img {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			right: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			border-radius: var(--theme-border-radius);
		}
		.big-image-card-pc-text-warp {
			position: absolute;
			left: 0;
			width: 100%;
			bottom: 0;
			padding: 18px;
			background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.57) 100%);
			border-radius: var(--theme-border-radius);
			.title {
				font-size: 16px;
				color: #fff;
				margin-bottom: 12px ;
				font-weight: 600;
				display: -webkit-box;         /* 必须结合的用于支持多行截断 */
				-webkit-line-clamp: 2;        /* 限制显示的行数 */
				-webkit-box-orient: vertical; /* 设置盒子布局为垂直方向 */
				overflow: hidden;             /* 隐藏多余的文本 */
				text-overflow: ellipsis;      /* 使用省略号表示截断的文本 */
			}
			.time {
				font-size: 12px;
				color: #fff;
				margin-bottom: 12px ;
				font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
			}
		}
	}
	
	
}
.text-card {
	border: 1px solid var(--border_color);
	display: block;
	border-radius: var(--theme-border-radius);
	background-color: var(--theme-pc-content-card-bg);
	.text-card-warp {
		width: 100%;
		padding: 18px;
		border-radius: var(--theme-border-radius);
		background-color: var(--theme-pc-content-card-bg);
		.title {
			font-size: 16px;
			color: #000;
			margin-bottom: 12px ;
			min-height: 48px;
			font-weight: 600;
			display: -webkit-box;         /* 必须结合的用于支持多行截断 */
			-webkit-line-clamp: 2;        /* 限制显示的行数 */
			-webkit-box-orient: vertical; /* 设置盒子布局为垂直方向 */
			overflow: hidden;             /* 隐藏多余的文本 */
			text-overflow: ellipsis;      /* 使用省略号表示截断的文本 */
		}
		.time {
			font-size: 12px;
			color: #9F9F9F;
			margin-bottom: 12px ;
			font-weight: normal;
			font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
		}
		.content {
			font-size: 14px;
			color: #000;
			font-weight: normal;
			display: -webkit-box;         /* 必须结合的用于支持多行截断 */
			-webkit-line-clamp: 9;        /* 限制显示的行数 */
			-webkit-box-orient: vertical; /* 设置盒子布局为垂直方向 */
			overflow: hidden;             /* 隐藏多余的文本 */
			text-overflow: ellipsis;      /* 使用省略号表示截断的文本 */
		}
	}
}

.image-card-category {
	position: absolute;
	left: 0;
	top: 20px;
	display: block;
	padding: 0 8px;
	font-size: 14px;
	background-color:  var(--theme-color);
	color: #fff;
}
