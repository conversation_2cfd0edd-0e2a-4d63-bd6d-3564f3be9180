.footer {
	width: 100%;
	height: 138px;
	flex-shrink: 0;
	background-color: var(--theme-color);
	display: flex;
	align-items: center;
	.foot-box {
		width: 1200px;
		margin: 0 auto;
		height: auto;
		display: flex;
		flex-direction: column;
		gap: 24px;
		.logo {
			width: 200px;
			height: 46px;
			background-repeat: no-repeat;
			background-position: center;
			background-size: contain;
		}
		.footer-warp {
			display: flex;
			justify-content: space-between;
			font-size: 14px;
			font-style: normal;
			line-height: normal;

			.left {
				color: #fff;
				font-family: "Plus Jakarta Sans";
				font-weight: 300;
			}
			.right {
				display: flex;
				gap: 24px;
				a {
					padding-right: 24px;
					border-right: 1px solid #fff;
					color: #fff;
					font-family: Afacad;
					font-weight: 400;
					text-transform: capitalize;
				}
				a:last-child {
					border-right: none;
				}
			}
		}
	}
}
.m-footer {
	width: 100%;
	height: 160px;
	background: var(--secondary-color);
	margin-top: 40px;
	padding: 16px 29px;
	position: relative;
	.top {
		display: flex;
		flex-direction: column; /* 改为垂直排列 */
		flex-wrap: wrap; /* 允许换行 */
		align-items: center; /* 水平居中 */
		justify-content: center; /* 垂直居中 */
		width: 100%;
		height: 80px; /* 设置固定高度以形成两行 */
		a {
			color: #4c5056;
			width: 50%; /* 每行两个元素 */
			font-family: Afacad;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
			text-transform: capitalize;
			white-space: nowrap;
			padding: 10px;
			text-align: center; /* 文本居中 */
			box-sizing: border-box; /* 确保padding不影响宽度计算 */
		}
	}
	.footer-mobile-social-icons {
		margin: 8px auto;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12px;
	}
	.bottom {
		text-align: center;
		color: #abaeb3;
		font-family: "Plus Jakarta Sans";
		font-size: 12px;
		font-style: normal;
		font-weight: 300;
		line-height: normal;
	}
}
