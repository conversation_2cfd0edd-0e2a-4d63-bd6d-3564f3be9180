import { useEffect, useImperativeHandle, useRef, useState } from "preact/hooks";
import cls from "classnames";
import "./style.less";
import { IEnvMap } from "@types";
import { getLocale } from "@utils/locales";
import { LazyImg, } from "@components";

interface IProps {
	seoEnvMap: IEnvMap;
}
export default function Language(props: IProps) {
	const { seoEnvMap } = props;

	const { languageList, lang, imgDomain, isMobile } = seoEnvMap;
	const [showLanguageModal, setShowLanguageModal] = useState(false);

	let languageIcon: string;
	let languageName: string;
	let languageCode: string;
	languageList.some((item) => {
		if (item.code === lang) {
			languageIcon = item.icon;
			languageName = item.name;
			languageCode = item.code;
			return true;
		}
	});


	const modalRef = useRef(null);
	const langRef = useRef(null);
	const toggleLanguageModal = () => {
		setShowLanguageModal(!showLanguageModal);
		// console.log("showLanguageModal", showLanguageModal);
		// if (!showLanguageModal && isMobile) {
		// 	document.getElementsByTagName("body")[0].style.overflow = "hidden";
		// } else {
		// 	document.getElementsByTagName("body")[0].style.overflow = "auto";
		// }



	};

	useEffect(() => {
		function handleClickOutside(event) {
			if ((modalRef.current && !modalRef.current.contains(event.target)) && (langRef.current && !langRef.current.contains(event.target))) {
				setShowLanguageModal(false);
			}
		}

		if (showLanguageModal) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showLanguageModal, setShowLanguageModal]);


	return (
		<section className={cls("language-wrap")}>
			<div className="language-content" onClick={toggleLanguageModal} ref={langRef}>
				<div className="language">
					<div className="language-icon">
						<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
							<path d="M12.5046 2C6.98465 2 2.51465 6.48 2.51465 12C2.51465 17.52 6.98465 22 12.5046 22C18.0346 22 22.5146 17.52 22.5146 12C22.5146 6.48 18.0346 2 12.5046 2ZM19.4346 8H16.4846C16.1712 6.76161 15.7078 5.56611 15.1046 4.44C16.9288 5.068 18.4658 6.33172 19.4346 8ZM12.5146 4.04C13.3446 5.24 13.9946 6.57 14.4246 8H10.6046C11.0346 6.57 11.6846 5.24 12.5146 4.04ZM4.77465 14C4.61465 13.36 4.51465 12.69 4.51465 12C4.51465 11.31 4.61465 10.64 4.77465 10H8.15465C8.07465 10.66 8.01465 11.32 8.01465 12C8.01465 12.68 8.07465 13.34 8.15465 14H4.77465ZM5.59465 16H8.54465C8.86465 17.25 9.32465 18.45 9.92465 19.56C8.0992 18.9344 6.56139 17.67 5.59465 16ZM8.54465 8H5.59465C6.56139 6.32995 8.0992 5.06561 9.92465 4.44C9.32153 5.56611 8.8581 6.76161 8.54465 8ZM12.5146 19.96C11.6846 18.76 11.0346 17.43 10.6046 16H14.4246C13.9946 17.43 13.3446 18.76 12.5146 19.96ZM14.8546 14H10.1746C10.0846 13.34 10.0146 12.68 10.0146 12C10.0146 11.32 10.0846 10.65 10.1746 10H14.8546C14.9446 10.65 15.0146 11.32 15.0146 12C15.0146 12.68 14.9446 13.34 14.8546 14ZM15.1046 19.56C15.7046 18.45 16.1646 17.25 16.4846 16H19.4346C18.4658 17.6683 16.9288 18.932 15.1046 19.56ZM16.8746 14C16.9546 13.34 17.0146 12.68 17.0146 12C17.0146 11.32 16.9546 10.66 16.8746 10H20.2546C20.4146 10.64 20.5146 11.31 20.5146 12C20.5146 12.69 20.4146 13.36 20.2546 14H16.8746Z" fill="black" />
						</svg>
					</div>
					<div className="language-name">{languageCode}</div>
				</div>

				<div className="language-arrow">
					<svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none">
						<path d="M8.99996 0.999999L4.75732 5.24264L0.514684 0.999999" stroke="black" stroke-linecap="round" />
					</svg>
				</div>
			</div>

			{
				showLanguageModal && (
					<div className="language-modal" ref={modalRef}>
						{
							isMobile ? <div className="back">
								<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" onClick={() => { toggleLanguageModal() }}>
									<path d="M19.8452 10.6152H6.94224L12.5794 4.97811C13.0299 4.52761 13.0299 3.78832 12.5794 3.33781C12.4725 3.23073 12.3455 3.14577 12.2058 3.0878C12.0661 3.02984 11.9163 3 11.765 3C11.6137 3 11.4639 3.02984 11.3241 3.0878C11.1844 3.14577 11.0575 3.23073 10.9506 3.33781L3.33818 10.9502C3.2311 11.0571 3.14614 11.184 3.08817 11.3237C3.0302 11.4635 3.00037 11.6133 3.00037 11.7646C3.00037 11.9159 3.0302 12.0657 3.08817 12.2054C3.14614 12.3451 3.2311 12.4721 3.33818 12.5789L10.9506 20.1913C11.0575 20.2983 11.1845 20.3831 11.3242 20.441C11.464 20.4989 11.6137 20.5286 11.765 20.5286C11.9162 20.5286 12.066 20.4989 12.2057 20.441C12.3454 20.3831 12.4724 20.2983 12.5794 20.1913C12.6863 20.0844 12.7711 19.9574 12.829 19.8177C12.8869 19.678 12.9167 19.5282 12.9167 19.3769C12.9167 19.2257 12.8869 19.0759 12.829 18.9362C12.7711 18.7965 12.6863 18.6695 12.5794 18.5626L6.94224 12.9255H19.8452C20.4806 12.9255 21.0004 12.4057 21.0004 11.7703C21.0004 11.135 20.4806 10.6152 19.8452 10.6152Z" fill="#0A0A0A" />
								</svg>
							</div> : null
						}
						{
							isMobile ?
								<div className="mobile-language-modal-content">
									<div className="language-modal-content">
										{
											languageList?.map(item => {
												return (
													// <LinkOrA path={`/${item.code}`} >
													// 	<div className="language-item" onClick={(event) => {
													// 		event.stopPropagation();
													// 		toggleLanguageModal();
													// 	}}>
													// 		<div className="language-icon">
													// 			<LazyImg src={`${imgDomain}/image/24*24/${item.icon}`} alt={item.name} />
													// 		</div>
													// 		<div className={cls("language-name", isMobile && item.code === lang ? "mobile-active" : "")}>{item.name}</div>
													// 	</div>
													// </LinkOrA>
													<div>{item.name}</div>
												)
											})
										}
									</div>
								</div>
								: <div className="language-modal-content">
									{
										languageList?.map(item => {
											return (
												<a href={`/${item.code}`} key={item}  >
													<div className="language-item" onClick={(event) => {
														event.stopPropagation();
														toggleLanguageModal();
													}}>
														{/* <div className="language-icon">
															<LazyImg src={`${imgDomain}/image/24*24/${item.icon}`} alt={item.name} />
														</div> */}
														<div className={cls("language-name", item.code === lang ? "mobile-active" : "")}>{item.name}</div>
													</div>
												</a>
											)
										})
									}
								</div>
						}


						{/* {
							isMobile ? <div className="Languageclose">
								<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none" onClick={() => { toggleLanguageModal() }}>
									<path d="M10.5 10.5L25.5 25.5M10.5 25.5L25.5 10.5" stroke="black" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
								</svg>
							</div> : null
						} */}
					</div>
				)
			}

		</section>
	);
}
