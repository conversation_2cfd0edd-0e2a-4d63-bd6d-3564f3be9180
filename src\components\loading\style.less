.loading-wrap{
	width: 100%;
	height: 30px;
	position: relative;
	.loading{
		position: absolute;
		top: calc(50% - 5px);
		left: calc(50% - 60px);
		z-index: 2;
	}
}
.loading,
.loading > div {
	position: relative;
	box-sizing: border-box;
}

.loading {
	display: block;
	font-size: 0;
	color: #000;
}

.loading.la-dark {
	color: #333;
}

.loading > div {
	display: inline-block;
	float: none;
	background-color: currentColor;
	border: 0 solid currentColor;
}

.loading {
	width: 120px;
	height: 10px;
	font-size: 0;
	text-align: center;
}

.loading > div {
	display: inline-block;
	width: 10px;
	height: 10px;
	white-space: nowrap;
	border-radius: 100%;
	animation: ball-elastic-dots-anim 1s infinite;
}

@keyframes ball-elastic-dots-anim {
	0%,
	100% {
		margin: 0;
		transform: scale(1);
	}

	50% {
		margin: 0 5%;
		transform: scale(0.65);
	}
}
