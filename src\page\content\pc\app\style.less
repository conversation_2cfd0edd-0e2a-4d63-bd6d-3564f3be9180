.content-wrap-pc {
	width: 100%;
	background-color: var(--theme-bg-color);
	min-height: 100%;

	.content-page-warp {
		.header-title {
			width: 100%;
			height: 49px;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #D9DCE2;
			background: color-mix(in srgb, #fff 80%, var(--theme-color) 20%);
			box-shadow: 0px 2px 4px 0px rgba(190, 190, 190, 0.25);
			// margin-top: 84px;

			.title-box {
				width: 1200px;
				margin: 0 auto;

				color: #000;
				font-family: Inter;
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: normal;
				text-transform: capitalize;
				display: flex;
				gap: 4px;

				.home {
					font-weight: 400;
					color: #000;
				}

				.category {
					font-weight: 700;
				}
			}
		}

		.content-body {
			display: flex;
			width: 1200px;
			margin: 0 auto;
			margin-top: 24px;
			margin-bottom: 32px;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 24px;

			.content-title {
				margin: 0 auto;
				width: 752px;
				color: #000;
				font-family: Inter;
				font-size: 32px;
				font-style: normal;
				font-weight: 600;
				line-height: normal;
				text-transform: capitalize;
			}

			.content-time {
				width: 752px;
				color: rgba(0, 0, 0, 0.30);
				font-family: Inter;
				font-size: 16px;
				font-style: normal;
				font-weight: 400;
				line-height: normal;
				text-transform: capitalize;
			}

			.img {
				width: 482px;
				height: 271px;

				.carousel {
					width: 100%;
					height: 100%;

					img {
						width: 100%;
						height: 100%;
						// border-radius: 24px;
					}
				}
			}

			.content-txt {

				h1,
				h2,
				h3,
				h4,
				h5,
				h6,
				hr,
				p,
				blockquote,
				dl,
				dt,
				dd,
				ul,
				ol,
				li,
				pre,
				fieldset,
				button,
				input,
				textarea,
				th,
				td {
					// margin: revert;
					// padding: revert;
				}

				img {
					max-width: 100%;
					height: auto;
					object-fit: cover;
				}

				.ad-wrap {
					margin: 0;
				}

				figure {
					margin: 0;
				}

			}

			.content-txt {
				width: 752px;
				color: #000;
				font-family: Inter;
				font-size: 16px;
				font-style: normal;
				font-weight: 400;
				line-height: normal;
				// text-transform: capitalize;
				display: flex;
				flex-direction: column;
				gap: 24px;
				align-items: center;
			}

			.content-txt {


				img {

					width: 482px;
					height: 271px;
					object-fit: cover;

				}
			}

			.not-found {
				width: 1200px;
				margin: 50px auto;
				text-align: center;
				color: #423f46;
				font-family: Lora;
				font-size: 36px;
				font-style: normal;
				font-weight: 700;
				line-height: normal;
				padding: 40px 0 26px;
				text-align: center;
				text-transform: capitalize;
			}
		}



	}
}