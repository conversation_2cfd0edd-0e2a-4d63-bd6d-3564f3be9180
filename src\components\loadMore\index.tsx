import './style.less';
import { useEffect, useRef } from 'preact/hooks';

interface IProps {
	loadMore: () => {}; // 加载更多数据的函数
  	hasMore: boolean; // 是否还有更多数据
}
export default function LoadMore(props: IProps) {
	const { loadMore, hasMore } = props;

	const observerRef = useRef<HTMLDivElement | null>(null);

	useEffect(() => {
		if (!observerRef.current) return;
		const observer = new IntersectionObserver(
		  (entries) => {
			if (entries[0].isIntersecting) {
				if (hasMore) {
					loadMore();
				}
			}
		  },
		  { rootMargin: '100px' } // 100px 提前加载
		);
	
		observer.observe(observerRef.current);
		return () => observer.disconnect();
	}, [hasMore, loadMore]);
	return (
		<>
			{
				hasMore ?
				<div className='spinner'  ref={observerRef} /> : <div className='no-more'>No More</div>
			}
		</>
	);
}
