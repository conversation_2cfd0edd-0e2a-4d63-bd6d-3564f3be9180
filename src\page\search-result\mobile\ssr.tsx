import { renderToString } from 'preact-render-to-string';
import App from './app';
import { getSsrHtml } from '@utils/ssr';
import { IContentList, IEnvMap, IF2eFiles } from '@types';


interface IProps {
	seoCategoryListData: Array<string>;
	seoActiveCategory: string;
	seoEnvMap: IEnvMap;
	seoNewsList: IContentList,
}
export default function SSR(env: string, props: IProps, f2eFiles: IF2eFiles) {
	const content = renderToString(
		<App {...props}/>
	);

	return getSsrHtml(env, 'search-result-mobile',  content, props, f2eFiles);
}
