import { useState, useEffect } from 'preact/hooks';
import './style.less';
import { fetchList } from '@stores';
import { IContent, IContentList, IEnvMap } from '@types';
import { HeaderPc, Footer, ContentCard } from '@components';
import { trackBiEvent } from '@utils';
import { getLocale } from '@utils/locales';
import LeftResult from './LeftResult';
import RightResult from './RightResult';
import EmptyPng from '@assets/images/empty.png'
const adsMap: any = {};
let category = '';
// 每次拉取条数
const PAGE_SIZE = 12;
let nowPage = 3;
let nowCategory: string;
let nowList: Array<IContent>;
// 是否启用拉取
let useFetch = false;
interface IProps {
	seoEnvMap: IEnvMap;
	seoSearchResult: string;
	seoSearchResultList: Array<IContent>,
	seoCategoryListData: any,
	seoCategoryListItemData: any,
}
export default function App(props: IProps) {
	const { seoEnvMap, seoSearchResultList, seoSearchResult, seoCategoryListData, seoCategoryListItemData } = props


	const { noHeader, theme, imgDomain, siteId, creativeId, lang, hostname } = seoEnvMap;
	const { logo } = theme || {};
	const [listData, setListData] = useState<Array<IContent>>(seoSearchResultList);
	const [loading, setLoading] = useState(false);
	const [time, setTime] = useState(null);
	const [nextData, setNextData] = useState([]);
	const startIndex = 0;
	const endIndex = Math.min(9, seoSearchResultList.length);

	// 发送事件
	const sendEvent = (event_name: string) => {
		const params: any = {
			event_name,
			page_id: category
		}
		if (event_name === 'page_leave') {
			params.page_stayduration = (new Date().getTime() - time) / 1000
		}
		trackBiEvent(event_name, params);
	}


	useEffect(() => {
		window.addEventListener('scroll', function () {
			if (!useFetch && window.innerHeight + window.scrollY + 100 > document.body.offsetHeight) {
				nowPage = nowPage + 1;
				useFetch = true;
				setLoading(true);
				fetchList({
					page_size: PAGE_SIZE,
					page_index: nowPage,
					category: 'seoCategoryListData[0]',
					last_published_time: nextData[nextData.length - 1]?.published_time,
					last_id: nextData[nextData.length - 1]?.id,
					site_id: siteId
				}).then((res: IContentList) => {
					const { data } = res;
					nowList = listData.concat(data)
					// console.log(nowList, 'nowList')
					setNextData(data)
					setListData(prevListData => {
						const nowList = prevListData.concat(data);
						// console.log(nowList, 'nowList');
						return nowList; // Return the new array for the updated state
					});
					// 当存在数据 且 当前不为最后一页
					if (data && data.length === PAGE_SIZE) {
						useFetch = false;
					}
				}).finally(() => {
					setLoading(false);
				});
			}
		});
	}, []);

	useEffect(() => {
		// 发送gtm事件: 首页进入事件
		// gtmEvent('search_result_pv', {
		// 	event_label: 'search-result'
		// });

		// adsComponentsInit().then(() => {
		// 	window.adsTag.cmd.push(() => {
		// 		Object.keys(adsMap).forEach(adsId => {
		// 			const { width, height, zoneId } = adsMap[adsId];
		// 			const dom = document.querySelector(`[data-ads-id="${adsId}"]`);
		// 			window.adsTag.renderAds(dom, width, height, zoneId);
		// 		});
		// 	});
		// });

		// category = activeCategory;
		setLoading(true);
		setTime(new Date().getTime());
	}, []);

	// 绑定埋点事件
	useEffect(() => {
		if (!loading) {
			return
		}
		sendEvent('page_reach');
	}, [loading])


	return (
		<section className="search-result-wrap-pc">

			<HeaderPc seoEnvMap={seoEnvMap} logo={logo} creativeId={creativeId} searchResult={seoSearchResult} seoCategoryListData={seoCategoryListData} />

			<div className="search-page-warp">
				<div className="search-result-name">
					<p>
						<span>{getLocale(lang, "results_for")}</span>‘
						<span className={"search-result-name-keyword"}>
							{seoSearchResult}
						</span>
						’
					</p>
				</div>
				<div className={'search-result-page-card-warp'}>
					{seoSearchResultList.length > 0 ? (
						<div className="search-result">
							<LeftResult seoEnvMap={seoEnvMap} seoNewsList={seoSearchResultList.slice(startIndex, endIndex)} />
							<RightResult seoEnvMap={seoEnvMap} seoNewsList={seoCategoryListItemData.slice(0, Math.min(seoCategoryListItemData.length, 5))} title={getLocale(lang,'recommend')} />
						</div>) : (
						<div className="empty-warp">
							<img src={EmptyPng} />
							<div className="empty-desc">
								{getLocale(lang, 'no search result yet')}
							</div>
						</div>
					)
					}
				</div>


			</div>


		</section>
	)
}
