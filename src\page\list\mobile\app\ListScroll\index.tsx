import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import { LazyImg, LinkOrA } from '@components';
import { getFormattedDate, getFormattedNumberDate, getScaleImgSrc } from '@utils';
import { IContent } from '@types';
import cls from 'classnames';
import TopNews from '../../../../index/mobile/app/LastNews/index';

interface IProps {
  item: Array<IContent>
  lang: string
  imgDomain: string
  category: string
  showTime: boolean
}
export default function ListScroll(props: IProps) {
  const { item, lang, imgDomain, category, showTime } = props;
  return (
    item && item.map((list, index) => {
      if (index % 4 === 0) {
        return (
          <LinkOrA path={`/${lang}/${list.path || list.id}`} className={cls("bottom-item", index !== 0 ? "bottom-margin" : "")}>
            <LazyImg src={getScaleImgSrc(list, imgDomain, '534*300')} alt={list.title} />
            <div className="bottom-item-info">
              <div className={cls("title")}><span className={"title-category"}>{category}</span>

                {
                  showTime && (
                    <span className="time-title"> - {getFormattedDate(list.published_time)}</span>
                  )
                }
              </div>
              <div className="desc">{list.title}</div>
            </div>
          </LinkOrA>
        )
      } else {
        return (
          <TopNews lang={lang} item={list} imgDomain={imgDomain} showTime={showTime} />
        )
      }

    })
  )
  // <LinkOrA path={`/${lang}/${item.path || item.id}`} className="scroll-item">
  //   <LazyImg src={getScaleImgSrc(item, imgDomain, '534*300')} alt={item.title} />
  //   <div className="scroll-item-info">
  //     <div className={cls("title",item.abstract ? "title-active" : "")}>{item.title}</div>
  //     {
  //       item.abstract && (
  //         <div className="desc">{item.abstract}</div>
  //       )
  //     }
  //   </div>
  // </LinkOrA>

}
