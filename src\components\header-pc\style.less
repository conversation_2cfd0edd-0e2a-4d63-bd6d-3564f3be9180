.header-component {
	width: 100%;
	height:84px;
	position: fixed;
	top: 0;
	z-index: 99;
	background: var(--header_bg_color);

	.header-wrap {
		width: 1200px;
		height: var(--header_height);
		margin: 0 auto;
		display: flex;
		align-items: center;
		position: relative;
		justify-content: space-between;

		.title {
			display: block;
			width: 200px;
			height: 46px;
			background-image: url('/src/assets/images/logoDefault.png');
			background-size: contain;
			background-position: center left;
			background-repeat: no-repeat;
			// margin-right: 8px;
		}

		.header-right {
			display: flex;
			align-items: center;
			border-radius: 0px;
		}

		.header-search {
			border-radius: 58px;
			background: #EBEDF3;
			display: flex;
			justify-content: start;
			align-items: center;
			
			width: 561px;
			padding: 10px 20px;
			align-items: flex-end;
			gap: 13px;

			img {
				width: 24px;
				height: 24px;
				margin-right: 12px;
				cursor: pointer;
			}

			input {

				width: 100%;
				outline: none;
				border: none;
				height: 26px;
				line-height: 26px;
				font-size: 18px;
				background: #EBEDF3;
				color: #000;
				font-family: Poppins;
				font-size: 14px;
				font-style: normal;
				font-weight: 500;
				line-height: normal;
				text-transform: capitalize;

				// &::placeholder {
				// 	font-size: 18px;
				// 	color: #D0D0D0;
				// }
			}
		}
	}
}