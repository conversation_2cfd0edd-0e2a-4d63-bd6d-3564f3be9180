.language-wrap {
	// position: fixed;
	// right: 24px;
	// top: 629px;
	margin-left: 23px;

	.language-content {
		display: flex;
		padding: 9px 8px;
		align-items: center;
		gap: 20px;
		cursor: pointer;

		.language {
			display: flex;
			align-items: center;
			gap: 8px;

			.language-icon {
				border-radius: 24px;
				border: 1px solid #F5F5F5;
				width: 24px;
				height: 24px;

				img {
					width: 100%;
					height: 100%;
					border-radius: 24px;
					border: 1px solid #F5F5F5;
					object-fit: cover;


				}
			}


			.language-name {
				color: #000;

				/* Poppins/text/16 */
				font-family: Poppins;
				font-size: 16px;
				font-style: normal;
				font-weight: 500;
				line-height: normal;
				text-transform: capitalize;
			}
		}
	}


	.language-modal {
		// width: 100%;
		// height: 100%;
		// background: rgba(0, 0, 0, 0.5);
		// border-radius: 12px;
		// background: #FFF;
		border-radius: 12px;
		background: rgba(41, 42, 53, 0.90);
		backdrop-filter: blur(2.5px);
		// box-shadow: 2px 4px 16.2px 0px rgba(0, 0, 0, 0.25);
		position: absolute;
		right: -10px;
		top: 77px;
		width: 292px;

		// padding: 10px 4px 7px 0;
		padding: 24px;

		.language-modal-content {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			gap: 15px;
			flex-wrap: wrap;
			padding-top: 4px;
			overflow-y: auto;
			height: 100%;


			// padding: 14px 0 0 16px;

			.language-item {
				display: flex;
				flex-direction: row;
				align-items: center;
				// gap: 8px;
				// padding: 9px 0 9px 12px;

				// width: 230px;
				// height: 59px;
				flex-shrink: 0;
				border-radius: 4px;


				// .language-icon {
				// 	border-radius: 17px;
				// 	border: 1px solid #EAF2FF;
				// 	width: 34px;
				// 	height: 34px;
				// 	flex-shrink: 0;

				// 	img {
				// 		width: 100%;
				// 		height: 100%;
				// 		border-radius: 17px;
				// 		border: 1px solid #EAF2FF;
				// 		object-fit: cover;
				// 	}
				// }


				.language-name {
					// color: #000;
					color: rgba(255, 255, 255, 0.70);
					text-align: center;
					color: rgba(255, 255, 255, 0.70);

					/* Josefin Sans/text/16 */
					font-family: "Josefin Sans";
					font-size: 16px;
					font-style: normal;
					font-weight: 500;
					line-height: normal;

				}

				.mobile-active {
					color: #FFF;
					font-family: Poppins;
					font-size: 16px;
					font-style: normal;
					font-weight: 700;
					line-height: normal;
					text-transform: capitalize;
				}


				&:hover {
					background: rgba(191, 227, 90, 0.10);
				}
			}
		}




	}

}