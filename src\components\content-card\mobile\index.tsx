import { useEffect, useState } from 'preact/hooks';
import cls from 'classnames';
import { getTimeSincePost, trackBiEvent, gtmEvent, appendCreativeIdToHref, getScaleImgSrc, getFormattedDate } from '@utils';
import './style.less';
import { IContent } from '@types';
import { LazyImg } from '@components';

interface IProps {
	content: IContent,
	category: string,
	imgDomain: string,
	creativeId: string,
	cardType: string
}
export default function ContentCardMobile(props: IProps) {
	const { content, category, imgDomain, creativeId, cardType } = props;
	let { id, title, published_time, type, path } = content;



	// type[1.站点内容, 2.search广告]
	const isSelfContent = type === 1;

	// const href = isSelfContent ? `/${id}` : link;
	const href = appendCreativeIdToHref(`/${path || id}`, creativeId);

	const onContentClick = () => {
		if (!isSelfContent) {
			return
		}
		const params = {
			event_name: 'news_click',
			page_id: category
		}
		trackBiEvent('news_click', params);

		// 发送gtm事件: 内容点击事件
		gtmEvent('item_click', {
			event_label: title
		});
	}

	let size: string

	if (cardType === 'smallImageCard') {
		size = '360*214'
		return (
			<a className={cls('small-image-mobile-card')} href={href} onClick={onContentClick}>
				<div className="img">
					<LazyImg src={getScaleImgSrc(content, imgDomain, size)} alt={title} />
					{
						category ? <span className="image-card-category">{category}</span>: null
					}
				</div>
				<div className="content-card-mobile-text-warp">
					<div className="title">
						{title}
					</div>
					<div className="time">
						{
							getFormattedDate(published_time)
						}
					</div>
				</div>
				
			</a>
		);
	} else if (cardType === 'bigImageCard'){
		size = '737*326'
		return (
			<a className={cls('big-image-mobile-card')} href={href} onClick={onContentClick}>
				<div className="big-image-card-warp">
					<LazyImg className="big-image-card-image"  src={getScaleImgSrc(content, imgDomain, size)} alt={title} />
					{
						category ? <span className="image-card-category">{category}</span>: null
					}
				</div>
				<div className="big-image-card-mobile-text-warp">
					<div className="title">
						{title}
					</div>
					<div className="time">
						{
							getFormattedDate(published_time)
						}
					</div>
				</div>
			</a>
		)
			
	}
}
