import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import { LazyImg, LinkOrA } from '@components';
import { getFormattedDate, getFormattedNumberDate, getScaleImgSrc } from '@utils';
import { IContent } from '@types';
import cls from 'classnames';

interface IProps {
  item: IContent
  lang: string
  showTime: boolean
  imgDomain: string
}
export default function TopNews(props: IProps) {
  const { item, lang, showTime, imgDomain } = props;
  return (
    <LinkOrA path={`/${lang}/${item.path?item.path:item.id}`} className="category-item">

      <LazyImg src={getScaleImgSrc(item, imgDomain, '263*184')} alt={item.title} />
      <div className="category-item-content" style={{ height: showTime ? '101px' : '88px' }}>

        <div className={cls("content-title", showTime?'content-title-time':"")}>{item.title}</div>

        {
          showTime && (
            <div className="content-two">{getFormattedDate(item.published_time)}</div>
          )
        }
      </div>

    </LinkOrA>
  )
}
