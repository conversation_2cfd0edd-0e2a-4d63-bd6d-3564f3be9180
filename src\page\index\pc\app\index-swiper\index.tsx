import { useState, useEffect, useRef } from 'preact/hooks';
import './style.less';
import { ContentCard } from '@components';
import LeftPng from '@assets/images/left.svg';
import RightPng from '@assets/images/right.svg';

interface IProps {
	swiperList: Array<any>;
	swipeWidth: number;
	swiperHeight: number;
	swiperGap: number;
	swiperName: string;
    creativeId: string;
    imgDomain: string;
}

// 存储滑动时位置
let mouseX: number;
let mouseY: number;
export default function IndexSwiper(props: IProps) {
	const { swiperList, swipeWidth, swiperHeight, swiperGap, swiperName, creativeId, imgDomain } = props;
	const [active, setActive] = useState(1);
	const container = useRef(null);

	useEffect(() => {
		const timer = setInterval(() => {
			handleNext();
		}, 3000);
		return () => {
			clearInterval(timer);
		};
	}, [active]);

	useEffect(() => {
		const dom = document.querySelector(`.${swiperName}`);
		dom.addEventListener('touchstart', touchStart, false);
		dom.addEventListener('touchmove', touchMove, false);
		dom.addEventListener('touchend', touchEnd);
		return () => {
			dom.removeEventListener('touchstart', touchStart, false);
			dom.removeEventListener('touchmove', touchMove, false);
			dom.removeEventListener('touchend', touchEnd);
		}
	}, [active])

	const handleChangeActive = (number: number) => {
		// container.current.style.transitionProperty = 'all';
		setActive(number);
	}

	const handlePrev = () => {
		handleChangeActive(active === 1 ? swiperList.length : active - 1);
	}

	const handleNext = () => {
		if (active === swiperList.length) {
			handleChangeActive(1);
		} else {
			handleChangeActive(active + 1);
		}
	}

	const touchStart = (e: TouchEvent) => {
		const { clientX, clientY }  = e.touches[0];
		mouseX = clientX;
		mouseY = clientY;
	}
	const touchMove = (e: TouchEvent) => {
		const { clientX, clientY }  = e.touches[0];
		const xScroll = Math.abs(mouseX - clientX);
		const yScroll = Math.abs(mouseY - clientY);
		if (xScroll > yScroll) {
			e.preventDefault();
		}
		if (mouseX && mouseX > clientX + 30) {
			handleNext();
			mouseX = null;
		}
		if (mouseX && mouseX < clientX - 30) {
			handlePrev();
			mouseX = null;
		}
	}
	const touchEnd = () => {
		mouseX = null;
		mouseY = null;
	}
	const containerWidth = swipeWidth + swiperGap;
	const left = (containerWidth - (swipeWidth - containerWidth) / 2) + (active - 2) * containerWidth;
	return (
		<div className="swiper-wrap" ref={container} style={{ paddingTop: swiperHeight }}> 
			<div
				style={{ height: swiperHeight, width: containerWidth * swiperList.length, transform: `translate3d(${-left}px,0,0)` }}
				className={`container ${swiperName}`}>
				{
					swiperList.map((item, index) => {
						return (
							<div className="items" key={index} style={{ paddingLeft: swiperGap / 2, paddingRight: swiperGap / 2 }}>
								<ContentCard cardType="bigImageCard" content={item}  key={item.id} creativeId={creativeId} imgDomain={imgDomain}/>
							</div>
						)
					})
				}
			</div>
			<div className="count-wrap">
				<div className="count">
					{
						swiperList.map((item, index) => (
							<div className={active === index + 1  ? "active" : "" } key={index} onClick={() => {
								handleChangeActive(index + 1)
							}}/>
						))
					}
				</div>
			</div>
		</div>
	)
}
