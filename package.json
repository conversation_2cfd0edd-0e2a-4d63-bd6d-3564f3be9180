{"name": "content-site", "version": "0.0.1", "components": "ContentSiteV21", "main": "./src/index.js", "scripts": {"start-win": "npm-run-all -p build:web start:web build:ssr-node-dev start:node", "start": "npm run build:web&npm run start:web&npm run build:ssr-node-dev&&npm run start:node", "start:web": "webpack-dev-server --config=webpack-dev.config.js --env.mock", "start:web-qa": "webpack-dev-server --config=webpack-dev.config.js  --env.qa", "start:web-pro": "webpack-dev-server --config=webpack-dev.config.js  --env.pro", "build:web": "cross-env webpack --config=webpack-build.config.js", "start:node": "cross-env ENV=development node server/index.js", "start:node-qa": "cross-env ENV=qa node server/index.js", "start:node-pro": "cross-env ENV=pro node server/index.js", "build:ssr-node-dev": "webpack --config=webpack-ssr-node.config.js", "build:ssr-node": "webpack --config=webpack-ssr-node.config.js", "connect:qa-win": "npm-run-all -p build:web start:web-qa build:ssr-node-dev start:node-qa", "connect:qa": "npm run build:web&npm run start:web-qa&npm run build:ssr-node-dev&&npm run start:node-qa", "connect:pro": "npm run build:web&npm run start:web-pro&npm run build:ssr-node-dev&&npm run start:node-pro", "connect:pro-win": "npm-run-all -p build:web build:ssr-node-dev start:web-pro start:node-pro", "lint": "eslint src"}, "dependencies": {"axios": "0.19.2", "classnames": "2.2.6", "history": "4.10.1", "preact": "^10.25.0", "preact-render-to-string": "^6.5.11", "wgt-node-utils": "^1.2.41"}, "author": "baukh", "private": true, "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-decorators": "^7.13.5", "@babel/plugin-proposal-export-default-from": "^7.12.13", "@babel/plugin-proposal-export-namespace-from": "^7.12.13", "@babel/plugin-proposal-function-sent": "^7.12.13", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.13.8", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-react-constant-elements": "^7.13.10", "@babel/plugin-transform-runtime": "^7.13.10", "@babel/preset-env": "^7.13.10", "@babel/preset-react": "^7.12.13", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.13.10", "@babel/runtime-corejs3": "^7.13.10", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "@typescript-eslint/eslint-plugin": "4.0.1", "@typescript-eslint/parser": "2.34.0", "autoprefixer": "^10.2.5", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "chalk": "^4.1.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^8.0.0", "cross-env": "^7.0.3", "css-loader": "^5.1.3", "css-minimizer-webpack-plugin": "^1.3.0", "cssnano": "^4.1.10", "eslint": "^7.22.0", "eslint-config-prettier": "8.1.0", "eslint-config-react-app": "^6.0.0", "eslint-loader": "^4.0.2", "eslint-plugin-flowtype": "^5.4.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "express": "^4.19.2", "file-loader": "^6.2.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-loader": "^2.1.2", "html-webpack-plugin": "^5.3.1", "jsx-loader": "^0.13.2", "less": "^4.1.1", "less-loader": "^8.0.0", "mini-css-extract-plugin": "^1.3.9", "mock-manager": "^0.1.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.40", "postcss-import": "14.0.0", "postcss-loader": "5.2.0", "prettier": "^2.2.1", "progress-bar-webpack-plugin": "^2.1.0", "resolve-url-loader": "^3.1.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^5.1.1", "ts-loader": "^9.2.6", "typescript": "^4.2.3", "ui-router-extras": "^0.1.3", "url-loader": "^4.1.1", "webpack": "^5.93.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "3.3.12", "webpack-dev-server": "^3.11.0", "webpack-merge": "^5.7.3", "yargs": "4.7.1", "optimize-css-assets-webpack-plugin": "^6.0.1"}, "sideEffects": ["*.css", "*.less"]}