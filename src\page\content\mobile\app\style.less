.content-page {
	padding: 20px 12px 45px;
	background: var(--theme-bg-color);
	min-height: 100vh;
	width: 100%;


	.content-wrap {
		margin-top: 8px;

		.content-text {

			.text-wrap {
				overflow: hidden;

				.carousel {
					height: 300px;

					img {
						width: 100%;
						height: 100%;
						border-radius: 5px;
					}
				}

				.title {
					color: #000;
					font-family: Inter;
					font-size: 20px;
					font-style: normal;
					font-weight: 500;
					line-height: normal;
					text-transform: capitalize;
					width: 100%;

					.title-time {
						display: block;
						color: #9F9F9F;
						color: rgba(0, 0, 0, 0.50);
						font-family: Inter;
						font-size: 16px;
						font-style: normal;
						font-weight: 400;
						line-height: normal;
						text-transform: capitalize;
						margin-top: 12px;
					}
				}

				.text-header {
					padding: 0;
					height: 23px;
					margin: 8px 8px 0 8px;

					h2 {
						padding: 0 11px;
						font-family: Inter;
						font-weight: 900;
						font-size: 16px;
					}
				}

				.text {
					text-overflow: ellipsis;
					word-break: break-all;
					display: -webkit-box;
					// margin: 0 15px;
					//-webkit-line-clamp: 3;
					-webkit-box-orient: vertical;
					overflow: hidden;

					color: #000;
					font-family: Inter;
					font-size: 16px;
					font-style: normal;
					font-weight: 400;
					line-height: normal;
					// text-transform: capitalize;
					width: 100%;
					margin-top: 14px;

					h1,
					h2,
					h3,
					h4,
					h5,
					h6,
					hr,
					p,
					blockquote,
					dl,
					dt,
					dd,
					ul,
					ol,
					li,
					pre,
					fieldset,
					button,
					input,
					textarea,
					th,
					td {
						margin: 10px 0 0;
						padding: revert;
					}

					h1,
					h2,
					h3 {
						font-family: Inter;
						font-weight: 900;
						font-size: 16px;
					}

					img {
						max-width: 100%;
						height: auto;
						border-radius: 8px;
						align-self: stretch;
						object-fit: cover;
						margin: 24px 0;
					}

					.ad-wrap {
						min-height: 250px;
						margin: 10px 0;
					}

					figure {
						margin: 0;
					}
				}
			}
		}

		.content-block {
			background: var(--bg_color);
		}
	}
}