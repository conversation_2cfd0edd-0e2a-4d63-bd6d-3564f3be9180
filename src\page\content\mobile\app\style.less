.content-wrap{
	.content-text{
		background: var(--bg_color);
		color: var(--current_category_color);
		margin: 24px 0 39px 0;
		padding: 0 15px;
		.text-wrap {
			overflow: hidden;
			.carousel {
				height: 300px;
				img {
					width: 100%;
					height: 100%;
					border-radius: 5px;
				}
			}
			.title{
				background: var(--bg_color);
				margin: 10px 5px;
				font-size: 20px;
				font-family: Arial Black;
				line-height: 30px;
				color: var(--current_category_color);
				border-bottom: 1px solid var(--border_color);
				.title-time{
					display: block;
					font-size: 12px;
					line-height: 20px;
					font-family: Aref Ruqaa;
					color: #9F9F9F;
					margin: 10px 0;
					font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
				}
			}
			.text-header{
				padding: 0;
				height: 23px;
				margin: 8px 8px 0 8px;
				h2{
					padding: 0 11px;
					font-family: <PERSON><PERSON>;
					font-weight: 900;
					font-size: 16px;
				}
			}
			.text{
				font-family: Aref Ruqaa;
				font-size: 14px;
				line-height: 29px;
				text-overflow: ellipsis;
				word-break: break-all;
				display: -webkit-box;
				margin: 0 15px;
				//-webkit-line-clamp: 3;
				-webkit-box-orient: vertical;
				overflow: hidden;
				h1, h2, h3, h4, h5, h6, hr, p, blockquote,
				dl, dt, dd, ul, ol, li, pre,
				fieldset, button, input, textarea,
				th, td {
					margin: 10px 0 0;
					padding: revert;
				}
				h1,h2,h3{
					font-family: Arial Black;
					font-weight: 900;
					font-size: 16px;
				}
				img{
					max-width: 100%;
					height: auto;
				}
				.ad-wrap {
					min-height: 250px;
					margin: 10px 0;
				}
				figure {
					margin: 0;
				}
			}
		}
	}
	.content-block{
		background: var(--bg_color);
	}
}
