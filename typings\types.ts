// 系统设备
export interface ISystem {
	isTablet: boolean;
	isPhone: boolean;
	isAndroid: boolean;
	isPC: boolean;
}

// 单条内容
export interface IContent {
	id: string;
	icon: string;
	title: string;
	link: string;
	type: number; // 内容类型: [1.站点内容, 2.外部链接]
	published_time: string;
	time_to_now: number;
	content?: string; // 列表没有
	categories?: Array<string>; // 列表没有
	author?: string; // 列表没有
	category?: Array<string>; // 列表没有
	category_name?: string; // 列表没有
	icon_id?: string,
	imgs?: Array<string>,
	path: string,
	abstract?: string
}

export interface IContentList {
	listData: Array<IContent>,
	category_name: string
}

// node 产出的环境变量
export interface IEnvMap {
	f2eOrigin: string;
	apiDomain: string;
	imgDomain: string;
	videoDomain: string;
	siteId: string;
	zoneMap: {
		[key: string]: string;
	};
	noHeader: boolean;
	timeEnable?: {
		[key: string]: boolean;
	}
	isMobile: boolean;
	theme: {
		title: string;
		icon: string;
		logo: string;
		[key: string]: string;
	}
	// 主域名 目前只有隐私页用到这个参数，后续还有别的地方使用，可以删除
	mainDomain?: string;
	creativeId: string;
	lang?: string
	languageList?: Array<any>
	hostname?: string
}

//technology List
export interface ITechnologyList {
	desc?: string
	icon?: string
	id?: string
	map?: string
	name?: string
	provider?: {
		one?: string
		two?: string
		three?: string
		four?: string
	},
	controller: string
	reviews?: string
	score?: string
	title?: string
}
export interface ITechnologyDetail {
	desc?: string
	headquarters?: string
	icon?: string
	id?: string
	map?: string
	name?: string
	provider?: {
		one?: string
		two?: string
		three?: string
		four?: string
	},
	reviews?: string
	score?: string
	title?: string
}

export interface IContentList {
	data: Array<IContent>,
	total: number
}

export interface IRenderAdsConfig {
	zone_key: string,
	position?: number,
	width: number,
	height: number
}

export interface ISeoEnvMap {
	apiDomain: string,
	hostname: string,
	siteId: string,
	zoneMap: string,
	searchId: string
}

// 语言配置
export interface IAboutRes {
	lan?: string,
	body?: string,
	error?: string
}

export interface IPolicyAndContact {
	contact?: string,
	txt?: string
}
// 前端资源文件
export interface IF2eFiles {
	jsStr: string;
	cssStr: string;
	preactStr: string;
	adsTagStr: string;
}
