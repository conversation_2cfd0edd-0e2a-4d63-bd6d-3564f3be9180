{"presets": [["@babel/preset-env", {"targets": "chrome>56, firefox>59", "useBuiltIns": "entry", "corejs": {"version": 3, "proposals": true}}], ["@babel/preset-react", {"pragma": "h", "pragmaFrag": "Fragment"}], "@babel/preset-typescript"], "plugins": ["@babel/plugin-proposal-optional-chaining", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-export-default-from", "@babel/plugin-transform-react-constant-elements", "@babel/plugin-transform-runtime", ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}]]}