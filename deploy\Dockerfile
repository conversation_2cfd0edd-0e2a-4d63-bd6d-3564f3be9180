# FROM *************:15000/common/nginx:1.16.0
FROM *************:15000/common/nginx-node:nginx1.16.1-node10.18.1
#FROM *************:15000/common-arm/nginx-node:nginx1.14.2-node10.18.1

ENV DEPLOY_DIR /dianyi/app/content_site
ENV CONF_DIR /etc/nginx/

WORKDIR $DEPLOY_DIR

COPY content-site $DEPLOY_DIR/content-site-tmp
COPY server $DEPLOY_DIR/server
COPY node_modules $DEPLOY_DIR/node_modules
COPY package.json $DEPLOY_DIR/
COPY api-domain.json $DEPLOY_DIR/
COPY host-site.json $DEPLOY_DIR/
COPY deploy/nginx.conf $CONF_DIR
COPY deploy/start.sh $DEPLOY_DIR/
RUN chmod 755 ${DEPLOY_DIR}
RUN chmod 755 ${DEPLOY_DIR}/start.sh

CMD ["bash", "-c", "$DEPLOY_DIR/start.sh" ]
